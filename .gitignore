# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json

# TypeScript
dist/
*.tsbuildinfo
build/

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production
.env.development.local
.env.test.local
.env.production.local

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Testing
coverage/

# Docker
.dockerignore
docker-compose.yml
docker-compose.override.yml

# Database
*.sqlite
*.db
*.sql
pgdata/

# Certificates and keys
*.pem
*.key
*.crt
*.cert
*.p12
*.pfx

# Secrets
*secret*
*credential*
*password*
*token*
!*example*
!*template*
