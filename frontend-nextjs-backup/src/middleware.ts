import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

const ADMIN_BASE_ROUTE = '/admin'
const ADMIN_DASHBOARD_ROUTE = ADMIN_BASE_ROUTE + '/dashboard'
const ADMIN_PROTECTED_ROUTES = [ADMIN_DASHBOARD_ROUTE]
const ADMIN_LOGIN_ROUTE = ADMIN_BASE_ROUTE + "/login"
const PUBLIC_ROUTES = ["/", ADMIN_LOGIN_ROUTE]


export function middleware(request: NextRequest) {

    const currentPath = request.nextUrl.pathname;

    const savedToken = request.headers.has('Authorization');
    const isAuthenticated = savedToken ?? false;

    if (ADMIN_PROTECTED_ROUTES.includes(currentPath) && !isAuthenticated) {
        const url = request.nextUrl.clone();
        url.pathname = ADMIN_LOGIN_ROUTE

        return NextResponse.redirect(url);
    }

    if (PUBLIC_ROUTES.includes(currentPath) && isAuthenticated) {
        const url = request.nextUrl.clone();
        url.pathname = ADMIN_DASHBOARD_ROUTE

        return NextResponse.redirect(url);
    }

    return NextResponse.next();
}