'use client';

import React from 'react';
import { cn } from '@/shared/lib/utils';

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, label, error, icon, ...props }, ref) => {
    return (
      <div className="space-y-2">
        {label && (
          <label
            htmlFor={props.id}
            className="block text-sm font-medium text-admin-textPrimary"
          >
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              {icon}
            </div>
          )}
          <input
            className={cn(
              "flex h-10 w-full rounded-md border border-admin-border bg-admin-surface px-3 py-2 text-sm text-admin-textPrimary placeholder:text-admin-textSecondary focus:outline-none focus:ring-2 focus:ring-admin-primary focus:border-admin-primary transition-colors",
              icon && "pl-10",
              error && "border-admin-error focus:ring-admin-error focus:border-admin-error",
              className
            )}
            ref={ref}
            {...props}
          />
        </div>
        {error && (
          <p className="text-admin-error text-sm">{error}</p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

export { Input };
