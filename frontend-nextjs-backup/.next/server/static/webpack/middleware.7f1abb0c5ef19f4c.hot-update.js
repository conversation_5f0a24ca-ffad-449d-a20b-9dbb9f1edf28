"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nconst ADMIN_BASE_ROUTE = '/admin';\nconst ADMIN_DASHBOARD_ROUTE = ADMIN_BASE_ROUTE + '/dashboard';\nconst ADMIN_PROTECTED_ROUTES = [\n    ADMIN_DASHBOARD_ROUTE\n];\nconst ADMIN_LOGIN_ROUTE = ADMIN_BASE_ROUTE + \"/login\";\nconst PUBLIC_ROUTES = [\n    \"/\",\n    ADMIN_LOGIN_ROUTE\n];\nfunction middleware(request) {\n    const currentPath = request.nextUrl.pathname;\n    const savedToken = request.headers.has('Authorization');\n    const isAuthenticated = savedToken ?? false;\n    if (ADMIN_PROTECTED_ROUTES.includes(currentPath) && !isAuthenticated) {\n        console.log(\"not logged in\");\n        const url = request.nextUrl.clone();\n        url.pathname = ADMIN_LOGIN_ROUTE;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    if (PUBLIC_ROUTES.includes(currentPath) && isAuthenticated) {\n        const url = request.nextUrl.clone();\n        url.pathname = ADMIN_DASHBOARD_ROUTE;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});