"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nconst ADMIN_BASE_ROUTE = '/admin';\nconst ADMIN_DASHBOARD_ROUTE = ADMIN_BASE_ROUTE + '/dashboard';\nconst ADMIN_PROTECTED_ROUTES = [\n    ADMIN_DASHBOARD_ROUTE\n];\nconst ADMIN_LOGIN_ROUTE = ADMIN_BASE_ROUTE + \"/login\";\nconst PUBLIC_ROUTES = [\n    \"/\",\n    ADMIN_LOGIN_ROUTE\n];\nfunction middleware(request) {\n    const currentPath = request.nextUrl.pathname;\n    const savedToken = request.headers.has('Authorization');\n    const isAuthenticated = savedToken ?? false;\n    console.log(\"handling redirection\");\n    if (ADMIN_PROTECTED_ROUTES.includes(currentPath) && !isAuthenticated) {\n        console.log(\"not logged in\");\n        const url = request.nextUrl.clone();\n        url.pathname = ADMIN_LOGIN_ROUTE;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    if (PUBLIC_ROUTES.includes(currentPath) && isAuthenticated) {\n        const url = request.nextUrl.clone();\n        url.pathname = ADMIN_DASHBOARD_ROUTE;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});