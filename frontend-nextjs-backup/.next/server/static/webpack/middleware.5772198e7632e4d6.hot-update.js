"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./src/middleware.ts":
/*!***************************!*\
  !*** ./src/middleware.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\nconst ADMIN_BASE_ROUTE = '/admin';\nconst ADMIN_DASHBOARD_ROUTE = ADMIN_BASE_ROUTE + '/dashboard';\nconst ADMIN_PROTECTED_ROUTES = [\n    ADMIN_DASHBOARD_ROUTE\n];\nconst ADMIN_LOGIN_ROUTE = ADMIN_BASE_ROUTE + \"/login\";\nconst PUBLIC_ROUTES = [\n    \"/\",\n    ADMIN_LOGIN_ROUTE\n];\nfunction middleware(request) {\n    const currentPath = request.nextUrl.pathname;\n    const savedToken = request.headers.has('Authorization');\n    const isAuthenticated = savedToken ?? false;\n    if (ADMIN_PROTECTED_ROUTES.includes(currentPath) && !isAuthenticated) {\n        const url = request.nextUrl.clone();\n        url.pathname = ADMIN_LOGIN_ROUTE;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    if (PUBLIC_ROUTES.includes(currentPath) && isAuthenticated) {\n        const url = request.nextUrl.clone();\n        url.pathname = ADMIN_DASHBOARD_ROUTE;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(url);\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKG1pZGRsZXdhcmUpLy4vc3JjL21pZGRsZXdhcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMkM7QUFHM0MsTUFBTUMsbUJBQW1CO0FBQ3pCLE1BQU1DLHdCQUF3QkQsbUJBQW1CO0FBQ2pELE1BQU1FLHlCQUF5QjtJQUFDRDtDQUFzQjtBQUN0RCxNQUFNRSxvQkFBb0JILG1CQUFtQjtBQUM3QyxNQUFNSSxnQkFBZ0I7SUFBQztJQUFLRDtDQUFrQjtBQUd2QyxTQUFTRSxXQUFXQyxPQUFvQjtJQUUzQyxNQUFNQyxjQUFjRCxRQUFRRSxPQUFPLENBQUNDLFFBQVE7SUFFNUMsTUFBTUMsYUFBYUosUUFBUUssT0FBTyxDQUFDQyxHQUFHLENBQUM7SUFDdkMsTUFBTUMsa0JBQWtCSCxjQUFjO0lBRXRDLElBQUlSLHVCQUF1QlksUUFBUSxDQUFDUCxnQkFBZ0IsQ0FBQ00saUJBQWlCO1FBQ2xFLE1BQU1FLE1BQU1ULFFBQVFFLE9BQU8sQ0FBQ1EsS0FBSztRQUNqQ0QsSUFBSU4sUUFBUSxHQUFHTjtRQUVmLE9BQU9KLHFEQUFZQSxDQUFDa0IsUUFBUSxDQUFDRjtJQUNqQztJQUVBLElBQUlYLGNBQWNVLFFBQVEsQ0FBQ1AsZ0JBQWdCTSxpQkFBaUI7UUFDeEQsTUFBTUUsTUFBTVQsUUFBUUUsT0FBTyxDQUFDUSxLQUFLO1FBQ2pDRCxJQUFJTixRQUFRLEdBQUdSO1FBRWYsT0FBT0YscURBQVlBLENBQUNrQixRQUFRLENBQUNGO0lBQ2pDO0lBRUEsT0FBT2hCLHFEQUFZQSxDQUFDbUIsSUFBSTtBQUM1QiIsInNvdXJjZXMiOlsiL3Jvb3QvdXJ1aHVzaHlhL2Zyb250ZW5kL3NyYy9taWRkbGV3YXJlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXNwb25zZSB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuaW1wb3J0IHR5cGUgeyBOZXh0UmVxdWVzdCB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuXG5jb25zdCBBRE1JTl9CQVNFX1JPVVRFID0gJy9hZG1pbidcbmNvbnN0IEFETUlOX0RBU0hCT0FSRF9ST1VURSA9IEFETUlOX0JBU0VfUk9VVEUgKyAnL2Rhc2hib2FyZCdcbmNvbnN0IEFETUlOX1BST1RFQ1RFRF9ST1VURVMgPSBbQURNSU5fREFTSEJPQVJEX1JPVVRFXVxuY29uc3QgQURNSU5fTE9HSU5fUk9VVEUgPSBBRE1JTl9CQVNFX1JPVVRFICsgXCIvbG9naW5cIlxuY29uc3QgUFVCTElDX1JPVVRFUyA9IFtcIi9cIiwgQURNSU5fTE9HSU5fUk9VVEVdXG5cblxuZXhwb3J0IGZ1bmN0aW9uIG1pZGRsZXdhcmUocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcblxuICAgIGNvbnN0IGN1cnJlbnRQYXRoID0gcmVxdWVzdC5uZXh0VXJsLnBhdGhuYW1lO1xuXG4gICAgY29uc3Qgc2F2ZWRUb2tlbiA9IHJlcXVlc3QuaGVhZGVycy5oYXMoJ0F1dGhvcml6YXRpb24nKTtcbiAgICBjb25zdCBpc0F1dGhlbnRpY2F0ZWQgPSBzYXZlZFRva2VuID8/IGZhbHNlO1xuXG4gICAgaWYgKEFETUlOX1BST1RFQ1RFRF9ST1VURVMuaW5jbHVkZXMoY3VycmVudFBhdGgpICYmICFpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgY29uc3QgdXJsID0gcmVxdWVzdC5uZXh0VXJsLmNsb25lKCk7XG4gICAgICAgIHVybC5wYXRobmFtZSA9IEFETUlOX0xPR0lOX1JPVVRFXG5cbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5yZWRpcmVjdCh1cmwpO1xuICAgIH1cblxuICAgIGlmIChQVUJMSUNfUk9VVEVTLmluY2x1ZGVzKGN1cnJlbnRQYXRoKSAmJiBpc0F1dGhlbnRpY2F0ZWQpIHtcbiAgICAgICAgY29uc3QgdXJsID0gcmVxdWVzdC5uZXh0VXJsLmNsb25lKCk7XG4gICAgICAgIHVybC5wYXRobmFtZSA9IEFETUlOX0RBU0hCT0FSRF9ST1VURVxuXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UucmVkaXJlY3QodXJsKTtcbiAgICB9XG5cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLm5leHQoKTtcbn0iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiQURNSU5fQkFTRV9ST1VURSIsIkFETUlOX0RBU0hCT0FSRF9ST1VURSIsIkFETUlOX1BST1RFQ1RFRF9ST1VURVMiLCJBRE1JTl9MT0dJTl9ST1VURSIsIlBVQkxJQ19ST1VURVMiLCJtaWRkbGV3YXJlIiwicmVxdWVzdCIsImN1cnJlbnRQYXRoIiwibmV4dFVybCIsInBhdGhuYW1lIiwic2F2ZWRUb2tlbiIsImhlYWRlcnMiLCJoYXMiLCJpc0F1dGhlbnRpY2F0ZWQiLCJpbmNsdWRlcyIsInVybCIsImNsb25lIiwicmVkaXJlY3QiLCJuZXh0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(middleware)/./src/middleware.ts\n");

/***/ })

});