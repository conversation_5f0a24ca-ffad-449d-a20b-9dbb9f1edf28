
# Rwanda Road Rules Learning App

## Overview
This project is a gamified learning platform aimed at teaching **Rwandan traffic rules and regulations**. Inspired by applications like **Duolingo**, it features:

- Modular courses and lessons
- Engaging quizzes with multimedia
- Gamification mechanics: XP, streaks, badges
- Admin dashboard for content management

The platform is designed with a modern tech stack, ensuring it is scalable, maintainable, and easy to deploy using containers.

---

## 🎯 Project Goals
- Promote understanding of Rwanda's traffic laws in a fun, memorable way
- Offer lesson-based progression with quizzes and badges
- Track user progress and provide personalized feedback
- Enable administrators to upload and manage course media and content

---

## 🧱 System Architecture

### Core Modules
- **User Management** (registration, XP, level, progress tracking)
- **Course/Lesson Management** (CRUD operations, media upload)
- **Quiz Engine** (questions, choices, scoring)
- **Gamification Engine** (XP calculation, streak logic, badges)
- **Media Storage Service** (for icons/images)
- **Admin Dashboard** (secured backend tools)

### API Services
RESTful services grouped by:
- `/api/users/`
- `/api/courses/`
- `/api/lessons/`
- `/api/quizzes/`
- `/api/media/`

---

## 🧑‍💻 Tech Stack

### Backend (API)
- **Language**: Node.js TypeScript
- **Framework**: Express.js
- **ORM**: Sequelize (for PostgreSQL)
- **Authentication**: JWT / OAuth2 (for future SSO)
- **Validation**: Express-validator
- **Port**: 4000 (configurable via environment variables)

### Database
- **PostgreSQL** (Relational DB)
- Hosted on: Docker or managed (e.g., Supabase, RDS)
- Graceful error handling for database connection issues

### Frontend (Optional Phase)
- **React.js (Next.js)**
- **Tailwind CSS**
- **Charting**: Recharts for dashboards

### Media Hosting
- Cloudinary or Firebase Storage for file uploads

### Containerization / Deployment
- **Docker**: For API, DB, and optional frontend
- **Docker Compose**: For multi-service orchestration
- **Nginx** (optional): API gateway/reverse proxy
- **CI/CD**: GitHub Actions or GitLab Pipelines
- **Hosting**: Railway, Render, or VPS

---

## 🧪 Development Workflow

### Folder Structure
```
project-root/
├── backend/
│   ├── src/
│   │   ├── controllers/
│   │   ├── models/
│   │   ├── routes/
│   │   ├── services/
│   │   └── utils/
│   ├── src/database/
│   ├── Dockerfile
│   └── docker-compose.yml
├── frontend/ (optional)
│   └── ...
└── README.md
```

### Key Configuration Files
- `.env`: Database and JWT secrets
- `docker-compose.yml`: Container services
- `src/models/`: Sequelize model definitions

---

## 🧩 Key Database Tables

### `users`
- user_id (PK)
- name
- email
- level
- xp
- created_at

### `courses`
- course_id (PK)
- title
- description
- icon_url
- banner_image

### `lessons`
- lesson_id (PK)
- course_id (FK)
- title
- content
- thumbnail_url
- illustration_url

### `quizzes`
- quiz_id (PK)
- lesson_id (FK)
- title

### `questions`
- question_id (PK)
- quiz_id (FK)
- text

### `choices`
- choice_id (PK)
- question_id (FK)
- text
- is_correct (boolean)

### `user_progress`
- user_id (FK)
- lesson_id (FK)
- completed (boolean)
- score
- streak

---

## 🧪 Docker & DevOps Setup

### Sample Dockerfile (Node Backend)
```Dockerfile
FROM node:18
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 4000
CMD ["npm", "run", "dev"]
```

### Sample Docker Compose
```yaml
version: '3.8'
services:
  api:
    build: ./backend
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=development
      - PORT=4000
      - DB_HOST=db
      - DB_PORT=6543
      - DB_NAME=rwanda_db
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - JWT_SECRET=your-super-secret-jwt-key-change-in-production
    depends_on:
      - db
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: rwanda_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "6543:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  pgdata:
```

---

## 📌 Next Steps
1. Set up PostgreSQL database (locally or with Docker)
2. Define initial seed data using Sequelize seeders
3. Set up database migrations with Sequelize-CLI
4. Build and test REST endpoints
5. Implement error handling and validation
6. Integrate media storage (Cloudinary API)
7. Scaffold frontend or admin dashboard
8. Launch on Docker for local testing
9. Push to remote and deploy on cloud

---

## 🏁 Summary
This project is a modular, scalable, and interactive traffic rules education platform with modern gamification. The use of **PostgreSQL** with **Sequelize ORM**, **Node.js**, and **Docker** ensures it’s ready for production, and expandable to mobile or internationalization later. The application features graceful error handling for database connections, making it robust even in environments where database connectivity might be intermittent.
