# Frontend Architecture Plan for Rwanda Traffic Learning App

## 📘 Project Overview

This document outlines the **frontend structure** of the Rwanda Road Rules Learning App using **Vue 3 + Vite**, built with a focus on clean architecture, composability, accessibility, and scalability.

---

## 🚀 Tech Stack

| Tool                             | Purpose                                                  |
| -------------------------------- | -------------------------------------------------------- |
| **Vue 3 + Vite**                 | App framework with file-based routing (via `vue-router`) |
| **TypeScript**                   | Static typing for reliability and DX                     |
| **Tailwind CSS**                 | Utility-first styling with design tokens                 |
| **Pinia**                        | Modular and scalable global state management             |
| **Vue Query**                    | Remote data fetching and caching                         |
| **VueUse**                       | Collection of essential Composition API utilities        |
| **Zod**                          | Schema validation and form handling                      |
| **Vitest + Vue Testing Library** | Testing suite for components & logic                     |
| **Motion One / VueUseMotion**    | Lightweight animation handling                           |

---

## 🌳 Project Structure (Clean Architecture)

```bash
/src
🔼📚 components/             # Global components
🔼📏 features/               # Feature-based modules (quiz, user, auth)
🔼🔼📖 quiz/
🔼🔼🔼📖 components/     # Feature-specific UI
🔼🔼🔼📝 composables/    # Composition API logic
🔼🔼🔼📊 services/       # API clients
🔼🔼🔼👌 types.ts
🔼🔼💡 stores/                 # Pinia store modules
🔼🔼🗍️ router/                 # App routes and guards
🔼🔼📒 shared/                 # Shared utilities, constants, types
🔼🔼🔼📚 components/         # Reusable generic components
🔼🔼🔼📝 composables/        # Custom composables/hooks
🔼🔼🔼💻 lib/                # Utilities and helpers
🔼🔼🔼🖋️ theme/              # Tailwind theme overrides
🔼🔼🔼💲 constants.ts
🔼🔼💄 styles/                 # Tailwind config and global styles
🔼🔼📄 tests/                  # Unit & integration tests
🔼🔼📃 types/                  # App-wide TypeScript declarations
```

---

## 🎨 Theming Strategy

### `/shared/theme/colors.ts`

```ts
export const adminPalette = {
  primary: '#0076ff',
  accent: '#f2cb0f',
  background: '#161616',
  surface: '#313131',
  textPrimary: '#f6f6f6',
  textSecondary: '#888888',
  border: '#444444',
};

export const userPalette = {
  primary: '#1e3a8a',
  accent: '#10b981',
  background: '#f9fafb',
  surface: '#ffffff',
  textPrimary: '#111827',
  textSecondary: '#6b7280',
  border: '#e5e7eb',
};
```

### Tailwind Config Integration

```js
// tailwind.config.js
const { adminPalette, userPalette } = require('./src/shared/theme/colors');

module.exports = {
  content: ["./index.html", "./src/**/*.{vue,js,ts}"],
  theme: {
    extend: {
      colors: {
        admin: adminPalette,
        user: userPalette,
      },
    },
  },
  plugins: [],
};
```

---

## 🧹 State Management with Pinia

### Sample Store

```ts
// stores/user.ts
import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  state: () => ({
    level: 1,
    xp: 0,
  }),
  actions: {
    gainXP(amount: number) {
      this.xp += amount;
    },
  },
});
```

### Usage in Component

```vue
<script setup lang="ts">
import { useUserStore } from '@/stores/user';
const user = useUserStore();
user.gainXP(50);
</script>
```

---

## 🔗 API Integration with Vue Query

### Setup API Client

```ts
// shared/lib/api.ts
import axios from 'axios';
export const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
});
```

### Fetching Example

```ts
import { useQuery } from '@tanstack/vue-query';

export function useUserProgress() {
  return useQuery({
    queryKey: ['userProgress'],
    queryFn: () => api.get('/progress').then(res => res.data),
  });
}
```

---

## 💫 Animations

Using [Motion One](https://motion.dev/) or [VueUseMotion](https://motion.vueuse.org/)

```vue
<script setup>
import { useMotion } from '@vueuse/motion';

const { motion } = useMotion();
</script>

<template>
  <div v-motion="'fade'">
    <QuizCard />
  </div>
</template>
```

Or with MotionOne directly:

```vue
<template>
  <div v-motion="{
    initial: { opacity: 0 },
    enter: { opacity: 1 },
  }">
    <UserPanel />
  </div>
</template>
```

---

## 🛡️ Best Practices

- Use `defineProps`/`defineEmits` + `<script setup>` for SFCs
- Use `Zod` for validation logic (`vue-zod-form`, or custom)
- Composition API over Options API
- Use `useHead()` (via [@vueuse/head](https://github.com/vueuse/head)) for SEO
- `v-motion` or `@vueuse/motion` for accessible, performant animations
- `ErrorBoundary` with `<Suspense>` and fallback states
- Structure components with logic split into `composables` for testability
- Prefer small, focused components over large monoliths

---

## ✅ Deployment Ready

- Vite-optimized build with tree-shaking
- Code splitting via `defineAsyncComponent`
- SEO-friendly via SSR (Nuxt/Vite SSR optional)
- Docker + CI/CD friendly via environment variables
- Lighthouse score optimized with Tailwind + lazy loading