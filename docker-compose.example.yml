version: '3.8'
services:
  api:
    build: ./backend
    ports:
      - "${PORT:-4000}:${PORT:-4000}"
    env_file:
      - ./backend/.env
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_CONTAINER_PORT:-5432}
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: >
      sh -c "npm run build &&
             npm run db:migrate &&
             npm run db:seed &&
             npm run dev"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-4000}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DB_NAME:-uruhushya_db}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "${DB_HOST_PORT:-6543}:${DB_CONTAINER_PORT:-5432}"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-postgres} -p ${DB_CONTAINER_PORT:-5432}"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  pgdata:
    name: ${COMPOSE_PROJECT_NAME:-uruhushya}-db-data
