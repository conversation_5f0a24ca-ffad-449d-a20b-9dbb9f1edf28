# Database Setup Guide

This guide explains how to set up the PostgreSQL database for the application using Docker Compose, including how to use environment variables and secrets for secure configuration.

## Option 1: Using Environment Variables (.env file)

This is the simplest approach and recommended for development environments.

### Step 1: Create or edit your .env file

Create a `.env` file in the root directory with the following content:

```
# Database Configuration
DB_NAME=uruhushya_db
DB_USER=your_db_username
DB_PASSWORD=your_secure_password
DB_HOST_PORT=6543
DB_CONTAINER_PORT=5432
```

You can change these values as needed. For example, if port 6543 is already in use on your system, you can change `DB_HOST_PORT` to another value.

### Step 2: Start the database container

```bash
docker-compose up -d db
```

## Option 2: Using Docker Secrets (More Secure)

This approach is more secure and recommended for production environments.

### Step 1: Create a secrets directory and files

```bash
mkdir -p secrets
echo "your_secure_password" > secrets/db_password.txt
```

Make sure to set appropriate permissions:

```bash
chmod 600 secrets/db_password.txt
```

### Step 2: Start the database container using the secrets configuration

```bash
docker-compose -f docker-compose.secrets.yml up -d db
```

## Connecting to the Database

Once your container is running, you can connect to it:

```bash
# Connect using psql client
psql -h localhost -p ${DB_HOST_PORT} -U ${DB_USER} -d ${DB_NAME}

# Or connect from inside the container
docker exec -it $(docker ps -q -f name=db) psql -U ${DB_USER} -d ${DB_NAME}
```

## Environment Variables

Here's an explanation of the environment variables used:

- `DB_NAME`: The name of the database to create
- `DB_USER`: The username for the database
- `DB_PASSWORD`: The password for the database user
- `DB_HOST_PORT`: The port on your host machine to map to the database container
- `DB_CONTAINER_PORT`: The port inside the container (usually 5432 for PostgreSQL)

## Security Best Practices

1. Never commit sensitive information like passwords to version control
2. Use `.gitignore` to exclude `.env` files and the `secrets` directory
3. Use strong, unique passwords for your database
4. Regularly rotate database passwords
5. For production, consider using a secrets management service like Docker Swarm secrets, Kubernetes secrets, or HashiCorp Vault

## Troubleshooting

### Port already in use

If you see an error like "port is already allocated", change the `DB_HOST_PORT` in your `.env` file to a different value.

### Connection refused

Make sure the database container is running:

```bash
docker ps
```

Check the container logs for any errors:

```bash
docker logs $(docker ps -q -f name=db)
```
