# Application Environment
# Options: development, test, production
NODE_ENV=development

# Server Configuration
PORT=4000

# Database Configuration
# For local development with Docker
DB_HOST=localhost
DB_PORT=6543                  # Port used by the application to connect to the database
DB_CONTAINER_PORT=5432        # Internal port used by PostgreSQL inside the container
DB_HOST_PORT=6543             # External port mapping for Docker (can be different from DB_CONTAINER_PORT)
DB_NAME=uruhushya_db
DB_USER=your_db_username
DB_PASSWORD=your_db_password

# Connection string format (optional - will be constructed from above if not provided)
# DATABASE_URL="postgresql://username:password@localhost:6543/database_name"

# Authentication
# IMPORTANT: Change this to a strong random string in production!
# You can generate one with: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
JWT_SECRET="CHANGE_THIS_TO_A_RANDOM_SECRET_KEY"
JWT_EXPIRES_IN="7d"
BCRYPT_SALT_ROUNDS=10

# CORS Configuration
# Use specific origins in production, e.g., "https://yourdomain.com,https://admin.yourdomain.com"
CORS_ORIGIN="*"

# Logging
# Options: error, warn, info, http, verbose, debug, silly
LOG_LEVEL="debug"

# Media Storage - Cloudinary Configuration
# CLOUDINARY_CLOUD_NAME=your_cloud_name
# CLOUDINARY_API_KEY=your_api_key
# CLOUDINARY_API_SECRET=your_api_secret

# Admin User Configuration (for initial setup)
ADMIN_NAME=Admin User
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password_immediately
