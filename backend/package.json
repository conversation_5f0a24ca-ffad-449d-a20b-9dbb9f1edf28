{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon --exec ts-node src/app.ts", "build": "tsc", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "db:create": "npx sequelize-cli db:create", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all", "db:reset": "npx sequelize-cli db:drop && npx sequelize-cli db:create && npx sequelize-cli db:migrate && npx sequelize-cli db:seed:all", "create:admin": "ts-node src/scripts/createAdminUser.ts", "test": "jest", "test:watch": "jest --watch"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}