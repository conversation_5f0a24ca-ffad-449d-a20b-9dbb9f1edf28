import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { CustomError } from '../utils/customError';
import User from '../models/user';

import { UserRole } from '../models/user';

export interface AuthRequest extends Request {
  user?: {
    id: string;
    email: string;
    role?: UserRole;
  };
}

export const authenticate = async (
  req: AuthRequest,
  _res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      throw new CustomError('No token provided', 401);
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, String(config.jwtSecret)) as {
      id: string;
      email: string;
      role?: UserRole;
    };

    const user = await User.findByPk(decoded.id, {
      attributes: ['id', 'email', 'role']
    });

    if (!user) {
      throw new CustomError('User not found', 401);
    }

    req.user = user;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new CustomError('Invalid token', 401));
    } else {
      next(error);
    }
  }
};
