import { Request, Response, NextFunction } from 'express';
import { CustomError } from '../utils/customError';
import { config } from '../config';

export const errorHandler = (
  err: Error,
  _req: Request,
  res: Response,
  _next: NextFunction
) => {
  if (err instanceof CustomError) {
    return res.status(err.statusCode).json({
      status: 'error',
      statusCode: err.statusCode,
      message: err.message,
    });
  }

  console.error('Error:', err);

  return res.status(500).json({
    status: 'error',
    statusCode: 500,
    message: config.nodeEnv === 'production'
      ? 'Internal server error'
      : err.message
  });
};
