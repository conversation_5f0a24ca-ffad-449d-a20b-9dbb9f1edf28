import { Response, NextFunction } from 'express';
import { CustomError } from '../utils/customError';
import { AuthRequest } from './authenticate';
import User from '../models/user';
import { UserRole } from '../models/user';

/**
 * Middleware to check if the authenticated user is an admin
 * This middleware should be used after the authenticate middleware
 */
export const adminAuth = async (
  req: AuthRequest,
  _res: Response,
  next: NextFunction
) => {
  try {
    if (!req.user) {
      throw new CustomError('Authentication required', 401);
    }

    const user = await User.findByPk(req.user.id, {
      attributes: ['id', 'email', 'role']
    });

    if (!user) {
      throw new CustomError('User not found', 404);
    }

    if (user.role !== UserRole.ADMIN) {
      throw new CustomError('Access denied. Admin privileges required', 403);
    }

    next();
  } catch (error) {
    next(error);
  }
};
