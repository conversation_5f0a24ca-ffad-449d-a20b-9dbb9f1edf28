import { Model, DataTypes, Optional, Association, HasManyGetAssociationsMixin, HasManyAddAssociationMixin, HasManyHasAssociationMixin, HasManyCountAssociationsMixin, HasManyCreateAssociationMixin, BelongsToGetAssociationMixin, BelongsToSetAssociationMixin, BelongsToCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import Quiz from './quiz';
import Choice from './choice';

interface QuestionAttributes {
  id: string;
  quizId: string;
  text: string;
}

interface QuestionCreationAttributes extends Optional<QuestionAttributes, 'id'> {}

class Question extends Model<QuestionAttributes, QuestionCreationAttributes> implements QuestionAttributes {
  public id!: string;
  public quizId!: string;
  public text!: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods - Quiz
  public getQuiz!: BelongsToGetAssociationMixin<Quiz>;
  public setQuiz!: BelongsToSetAssociationMixin<Quiz, string>;
  public createQuiz!: BelongsToCreateAssociationMixin<Quiz>;

  // Association methods - Choice
  public getChoices!: HasManyGetAssociationsMixin<Choice>;
  public addChoice!: HasManyAddAssociationMixin<Choice, string>;
  public hasChoice!: HasManyHasAssociationMixin<Choice, string>;
  public countChoices!: HasManyCountAssociationsMixin;
  public createChoice!: HasManyCreateAssociationMixin<Choice>;

  // Association declarations
  public readonly quiz?: Quiz;
  public readonly choices?: Choice[];

  public static associations: {
    quiz: Association<Question, Quiz>;
    choices: Association<Question, Choice>;
  };
}

Question.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    quizId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'quiz_id',
      references: {
        model: 'quizzes',
        key: 'id'
      }
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Question',
    tableName: 'questions',
    timestamps: true,
    underscored: true
  }
);

export default Question;
