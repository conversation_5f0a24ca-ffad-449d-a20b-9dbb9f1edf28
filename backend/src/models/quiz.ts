import { Model, DataTypes, Optional, Association, HasManyGetAssociationsMixin, HasManyAddAssociationMixin, HasManyHasAssociationMixin, HasManyCountAssociationsMixin, HasManyCreateAssociationMixin, BelongsToGetAssociationMixin, BelongsToSetAssociationMixin, BelongsToCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import Lesson from './lesson';
import Question from './question';

interface QuizAttributes {
  id: string;
  lessonId: string;
  title: string;
}

interface QuizCreationAttributes extends Optional<QuizAttributes, 'id'> {}

class Quiz extends Model<QuizAttributes, QuizCreationAttributes> implements QuizAttributes {
  public id!: string;
  public lessonId!: string;
  public title!: string;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods - Lesson
  public getLesson!: BelongsToGetAssociationMixin<Lesson>;
  public setLesson!: BelongsToSetAssociationMixin<Lesson, string>;
  public createLesson!: BelongsToCreateAssociationMixin<Lesson>;

  // Association methods - Question
  public getQuestions!: HasManyGetAssociationsMixin<Question>;
  public addQuestion!: HasManyAddAssociationMixin<Question, string>;
  public hasQuestion!: HasManyHasAssociationMixin<Question, string>;
  public countQuestions!: HasManyCountAssociationsMixin;
  public createQuestion!: HasManyCreateAssociationMixin<Question>;

  // Association declarations
  public readonly lesson?: Lesson;
  public readonly questions?: Question[];

  public static associations: {
    lesson: Association<Quiz, Lesson>;
    questions: Association<Quiz, Question>;
  };
}

Quiz.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    lessonId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'lesson_id',
      references: {
        model: 'lessons',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    }
  },
  {
    sequelize,
    modelName: 'Quiz',
    tableName: 'quizzes',
    timestamps: true,
    underscored: true
  }
);

export default Quiz;
