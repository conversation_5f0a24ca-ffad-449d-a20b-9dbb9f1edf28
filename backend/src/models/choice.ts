import { Model, DataTypes, Optional, Association, BelongsToGetAssociationMixin, BelongsToSetAssociationMixin, BelongsToCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import Question from './question';

interface ChoiceAttributes {
  id: string;
  questionId: string;
  text: string;
  isCorrect: boolean;
}

interface ChoiceCreationAttributes extends Optional<ChoiceAttributes, 'id' | 'isCorrect'> {}

class Choice extends Model<ChoiceAttributes, ChoiceCreationAttributes> implements ChoiceAttributes {
  public id!: string;
  public questionId!: string;
  public text!: string;
  public isCorrect!: boolean;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods - Question
  public getQuestion!: BelongsToGetAssociationMixin<Question>;
  public setQuestion!: BelongsToSetAssociationMixin<Question, string>;
  public createQuestion!: BelongsToCreateAssociationMixin<Question>;

  // Association declarations
  public readonly question?: Question;

  public static associations: {
    question: Association<Choice, Question>;
  };
}

Choice.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    questionId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'question_id',
      references: {
        model: 'questions',
        key: 'id'
      }
    },
    text: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    isCorrect: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_correct'
    }
  },
  {
    sequelize,
    modelName: 'Choice',
    tableName: 'choices',
    timestamps: true,
    underscored: true
  }
);

export default Choice;
