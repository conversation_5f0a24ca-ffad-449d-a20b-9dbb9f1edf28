import { Model, DataTypes, Optional, Association, HasManyGetAssociationsMixin, HasManyAddAssociationMixin, HasManyHasAssociationMixin, HasManyCountAssociationsMixin, HasManyCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import UserProgress from './userProgress';

// Define user roles
export enum UserRole {
  ADMIN = 'admin',
  TEACHER = 'teacher'
}

interface UserAttributes {
  id: string;
  name: string;
  email: string;
  password: string;
  role: UserRole;
  level: number;
  xp: number;
  createdAt: Date;
}

interface UserCreationAttributes extends Optional<UserAttributes, 'id' | 'level' | 'xp' | 'createdAt'> {}

class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: string;
  public name!: string;
  public email!: string;
  public password!: string;
  public role!: UserRole;
  public level!: number;
  public xp!: number;
  public createdAt!: Date;

  // Timestamps
  public readonly updatedAt!: Date;

  // Association methods
  public getProgress!: HasManyGetAssociationsMixin<UserProgress>;
  public addProgress!: HasManyAddAssociationMixin<UserProgress, string>;
  public hasProgress!: HasManyHasAssociationMixin<UserProgress, string>;
  public countProgress!: HasManyCountAssociationsMixin;
  public createProgress!: HasManyCreateAssociationMixin<UserProgress>;

  // Association declarations
  public readonly progress?: UserProgress[];

  public static associations: {
    progress: Association<User, UserProgress>;
  };
}

User.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.ENUM(...Object.values(UserRole)),
      allowNull: false,
      defaultValue: UserRole.TEACHER
    },
    level: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1
    },
    xp: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      field: 'created_at'
    }
  },
  {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    underscored: true
  }
);

export default User;
