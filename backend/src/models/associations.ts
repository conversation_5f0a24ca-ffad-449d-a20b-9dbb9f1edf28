import User from './user';
import Course from './course';
import Lesson from './lesson';
import Quiz from './quiz';
import Question from './question';
import Choice from './choice';
import UserProgress from './userProgress';
import { HasManyOptions, BelongsToOptions } from 'sequelize';

// Define associations
export const setupAssociations = () => {
  // User <-> UserProgress
  (User as any).hasMany(UserProgress, {
    foreignKey: 'userId',
    as: 'progress'
  } as HasManyOptions);

  (UserProgress as any).belongsTo(User, {
    foreignKey: 'userId'
  } as BelongsToOptions);

  // Course <-> Lesson
  (Course as any).hasMany(Lesson, {
    foreignKey: 'courseId',
    as: 'lessons'
  } as HasManyOptions);

  (Lesson as any).belongsTo(Course, {
    foreignKey: 'courseId',
    as: 'course'
  } as BelongsToOptions);

  // Lesson <-> Quiz
  (Lesson as any).hasMany(Quiz, {
    foreignKey: 'lessonId',
    as: 'quizzes'
  } as HasManyOptions);

  (Quiz as any).belongsTo(Lesson, {
    foreignKey: 'lessonId',
    as: 'lesson'
  } as BelongsToOptions);

  // Lesson <-> UserProgress
  (Lesson as any).hasMany(UserProgress, {
    foreignKey: 'lessonId'
  } as HasManyOptions);

  (UserProgress as any).belongsTo(Lesson, {
    foreignKey: 'lessonId',
    as: 'lesson'
  } as BelongsToOptions);

  // Quiz <-> Question
  (Quiz as any).hasMany(Question, {
    foreignKey: 'quizId',
    as: 'questions'
  } as HasManyOptions);

  (Question as any).belongsTo(Quiz, {
    foreignKey: 'quizId',
    as: 'quiz'
  } as BelongsToOptions);

  // Question <-> Choice
  (Question as any).hasMany(Choice, {
    foreignKey: 'questionId',
    as: 'choices'
  } as HasManyOptions);

  (Choice as any).belongsTo(Question, {
    foreignKey: 'questionId',
    as: 'question'
  } as BelongsToOptions);
};
