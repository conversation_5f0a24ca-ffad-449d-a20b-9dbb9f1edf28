import { Model, DataTypes, Optional, Association, HasManyGetAssociationsMixin, HasManyAddAssociationMixin, HasManyHasAssociationMixin, HasManyCountAssociationsMixin, HasManyCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import Lesson from './lesson';

export enum CourseLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced'
}

export enum CourseStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  ARCHIVED = 'archived'
}

interface CourseAttributes {
  id: string;
  title: string;
  description: string;
  level: CourseLevel;
  duration: number; // in hours
  status: CourseStatus;
  iconUrl: string | null;
  imageUrl: string | null;
}

interface CourseCreationAttributes extends Optional<CourseAttributes, 'id' | 'status'> {}

class Course extends Model<CourseAttributes, CourseCreationAttributes> implements CourseAttributes {
  public id!: string;
  public title!: string;
  public description!: string;
  public level!: CourseLevel;
  public duration!: number;
  public status!: CourseStatus;
  public iconUrl!: string | null;
  public imageUrl!: string | null;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods
  public getLessons!: HasManyGetAssociationsMixin<Lesson>;
  public addLesson!: HasManyAddAssociationMixin<Lesson, string>;
  public hasLesson!: HasManyHasAssociationMixin<Lesson, string>;
  public countLessons!: HasManyCountAssociationsMixin;
  public createLesson!: HasManyCreateAssociationMixin<Lesson>;

  // Association declarations
  public readonly lessons?: Lesson[];

  public static associations: {
    lessons: Association<Course, Lesson>;
  };
}

Course.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    level: {
      type: DataTypes.ENUM(...Object.values(CourseLevel)),
      allowNull: false,
      defaultValue: CourseLevel.BEGINNER
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      validate: {
        min: 1
      }
    },
    status: {
      type: DataTypes.ENUM(...Object.values(CourseStatus)),
      allowNull: false,
      defaultValue: CourseStatus.DRAFT
    },
    iconUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'icon_url'
    },
    imageUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'image_url'
    }
  },
  {
    sequelize,
    modelName: 'Course',
    tableName: 'courses',
    timestamps: true,
    underscored: true
  }
);

export default Course;
