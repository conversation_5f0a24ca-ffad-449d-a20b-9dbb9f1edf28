import { Model, DataTypes, Optional, Association, HasManyGetAssociationsMixin, HasManyAddAssociationMixin, HasManyHasAssociationMixin, HasManyCountAssociationsMixin, HasManyCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import Lesson from './lesson';

interface CourseAttributes {
  id: string;
  title: string;
  description: string;
  iconUrl: string | null;
  bannerImage: string | null;
  isPublished: boolean;
}

interface CourseCreationAttributes extends Optional<CourseAttributes, 'id' | 'iconUrl' | 'bannerImage'> {}

class Course extends Model<CourseAttributes, CourseCreationAttributes> implements CourseAttributes {
  public id!: string;
  public title!: string;
  public description!: string;
  public iconUrl!: string | null;
  public bannerImage!: string | null;
  public isPublished!: boolean

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods
  public getLessons!: HasManyGetAssociationsMixin<Lesson>;
  public addLesson!: HasManyAddAssociationMixin<Lesson, string>;
  public hasLesson!: HasManyHasAssociationMixin<Lesson, string>;
  public countLessons!: HasManyCountAssociationsMixin;
  public createLesson!: HasManyCreateAssociationMixin<Lesson>;

  // Association declarations
  public readonly lessons?: Lesson[];

  public static associations: {
    lessons: Association<Course, Lesson>;
  };
}

Course.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    iconUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'icon_url'
    },
    bannerImage: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'banner_image'
    },
    isPublished: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      field: 'is_published'
    }
  },
  {
    sequelize,
    modelName: 'Course',
    tableName: 'courses',
    timestamps: true,
    underscored: true
  }
);

export default Course;
