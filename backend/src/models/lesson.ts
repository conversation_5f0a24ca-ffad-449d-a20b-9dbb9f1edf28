import { Model, DataTypes, Optional, Association, HasManyGetAssociationsMixin, HasManyAddAssociationMixin, HasManyHasAssociationMixin, HasManyCountAssociationsMixin, HasManyCreateAssociationMixin, BelongsToGetAssociationMixin, BelongsToSetAssociationMixin, BelongsToCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import Course from './course';
import Quiz from './quiz';
import UserProgress from './userProgress';

interface LessonAttributes {
  id: string;
  courseId: string;
  title: string;
  content: string;
  thumbnailUrl: string | null;
  illustrationUrl: string | null;
}

interface LessonCreationAttributes extends Optional<LessonAttributes, 'id' | 'thumbnailUrl' | 'illustrationUrl'> {}

class Lesson extends Model<LessonAttributes, LessonCreationAttributes> implements LessonAttributes {
  public id!: string;
  public courseId!: string;
  public title!: string;
  public content!: string;
  public thumbnailUrl!: string | null;
  public illustrationUrl!: string | null;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods - Course
  public getCourse!: BelongsToGetAssociationMixin<Course>;
  public setCourse!: BelongsToSetAssociationMixin<Course, string>;
  public createCourse!: BelongsToCreateAssociationMixin<Course>;

  // Association methods - Quiz
  public getQuizzes!: HasManyGetAssociationsMixin<Quiz>;
  public addQuiz!: HasManyAddAssociationMixin<Quiz, string>;
  public hasQuiz!: HasManyHasAssociationMixin<Quiz, string>;
  public countQuizzes!: HasManyCountAssociationsMixin;
  public createQuiz!: HasManyCreateAssociationMixin<Quiz>;

  // Association methods - UserProgress
  public getUserProgresses!: HasManyGetAssociationsMixin<UserProgress>;
  public addUserProgress!: HasManyAddAssociationMixin<UserProgress, string>;
  public hasUserProgress!: HasManyHasAssociationMixin<UserProgress, string>;
  public countUserProgresses!: HasManyCountAssociationsMixin;
  public createUserProgress!: HasManyCreateAssociationMixin<UserProgress>;

  // Association declarations
  public readonly course?: Course;
  public readonly quizzes?: Quiz[];
  public readonly userProgresses?: UserProgress[];

  public static associations: {
    course: Association<Lesson, Course>;
    quizzes: Association<Lesson, Quiz>;
    userProgresses: Association<Lesson, UserProgress>;
  };
}

Lesson.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    courseId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'course_id',
      references: {
        model: 'courses',
        key: 'id'
      }
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    thumbnailUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'thumbnail_url'
    },
    illustrationUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      field: 'illustration_url'
    }
  },
  {
    sequelize,
    modelName: 'Lesson',
    tableName: 'lessons',
    timestamps: true,
    underscored: true
  }
);

export default Lesson;
