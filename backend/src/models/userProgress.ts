import { Model, DataTypes, Optional, Association, BelongsToGetAssociationMixin, BelongsToSetAssociationMixin, BelongsToCreateAssociationMixin } from 'sequelize';
import sequelize from '../config/database';
import User from './user';
import Lesson from './lesson';

interface UserProgressAttributes {
  id: string;
  userId: string;
  lessonId: string;
  completed: boolean;
  score: number;
  streak: number;
}

interface UserProgressCreationAttributes extends Optional<UserProgressAttributes, 'id' | 'completed' | 'score' | 'streak'> {}

class UserProgress extends Model<UserProgressAttributes, UserProgressCreationAttributes> implements UserProgressAttributes {
  public id!: string;
  public userId!: string;
  public lessonId!: string;
  public completed!: boolean;
  public score!: number;
  public streak!: number;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Association methods
  public getUser!: BelongsToGetAssociationMixin<User>;
  public setUser!: BelongsToSetAssociationMixin<User, string>;
  public createUser!: BelongsToCreateAssociationMixin<User>;

  public getLesson!: BelongsToGetAssociationMixin<Lesson>;
  public setLesson!: BelongsToSetAssociationMixin<Lesson, string>;
  public createLesson!: BelongsToCreateAssociationMixin<Lesson>;

  // Association declarations
  public readonly user?: User;
  public readonly lesson?: Lesson;

  public static associations: {
    user: Association<UserProgress, User>;
    lesson: Association<UserProgress, Lesson>;
  };
}

UserProgress.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'user_id',
      references: {
        model: 'users',
        key: 'id'
      }
    },
    lessonId: {
      type: DataTypes.UUID,
      allowNull: false,
      field: 'lesson_id',
      references: {
        model: 'lessons',
        key: 'id'
      }
    },
    completed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    score: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    },
    streak: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0
    }
  },
  {
    sequelize,
    modelName: 'UserProgress',
    tableName: 'user_progress',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'lesson_id']
      }
    ]
  }
);

export default UserProgress;
