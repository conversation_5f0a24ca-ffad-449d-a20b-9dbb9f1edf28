export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  statusCode: number;
  message?: string;
  data?: T;
}

export interface PaginatedResponse<T> extends ApiResponse {
  data: {
    items: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface TokenResponse {
  token: string;
  expiresIn: string;
}
