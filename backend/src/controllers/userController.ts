import { Request, Response, NextFunction } from 'express';
import { UserService } from '../services/userService';
import { CustomError } from '../utils/customError';
import { AuthRequest } from '../middleware/authenticate';
import { UserRole } from '../models/user';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  createUser = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { name, email, password, role } = req.body;

      if (!name || !email || !password || !role) {
        throw new CustomError('Missing required fields', 400);
      }

      // Validate role
      if (!Object.values(UserRole).includes(role)) {
        throw new CustomError('Invalid role', 400);
      }

      const user = await this.userService.createUser({ name, email, password, role });

      // Remove password from response
      const { password: _, ...userObject } = user.get({ plain: true });

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: userObject
      });
    } catch (error) {
      next(error);
    }
  };

  login = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        throw new CustomError('Missing required fields', 400);
      }

      const authData = await this.userService.login({ email, password });

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: authData
      });
    } catch (error) {
      next(error);
    }
  };

  getProfile = async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user?.id) {
        throw new CustomError('User not authenticated', 401);
      }

      const user = await this.userService.getUserById(req.user.id);

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user.get({ plain: true });

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: userWithoutPassword
      });
    } catch (error) {
      next(error);
    }
  };

  // Admin-only method to get all users
  getAllUsers = async (_req: Request, res: Response, next: NextFunction) => {
    try {
      const users = await this.userService.getAllUsers();

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: users
      });
    } catch (error) {
      next(error);
    }
  };
}
