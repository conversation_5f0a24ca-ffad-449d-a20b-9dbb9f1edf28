import { Request, Response, NextFunction } from 'express';
import { LessonService } from '../services/lessonService';
import { CustomError } from '../utils/customError';

export class LessonController {
  private lessonService: LessonService;

  constructor() {
    this.lessonService = new LessonService();
  }

  getLessonsByCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { courseId } = req.params;
      const lessons = await this.lessonService.getLessonsByCourse(courseId);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lessons
      });
    } catch (error) {
      next(error);
    }
  };

  getLessonById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const lesson = await this.lessonService.getLessonById(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };

  createLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { courseId, title, content, thumbnailUrl, illustrationUrl } = req.body;

      if (!courseId || !title || !content) {
        throw new CustomError('Missing required fields', 400);
      }

      const lesson = await this.lessonService.createLesson({
        courseId,
        title,
        content,
        thumbnailUrl,
        illustrationUrl
      });

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };

  updateLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const lesson = await this.lessonService.updateLesson(id, req.body);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: lesson
      });
    } catch (error) {
      next(error);
    }
  };

  deleteLesson = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      await this.lessonService.deleteLesson(id);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };
}
