import { Request, Response, NextFunction } from 'express';
import { CourseService } from '../services/courseService';
import { CustomError } from '../utils/customError';

export class CourseController {
  private courseService: CourseService;

  constructor() {
    this.courseService = new CourseService();
  }

  getAllCourses = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const courses = await this.courseService.getAllCourses(req.query);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: courses
      });
    } catch (error) {
      next(error);
    }
  };

  getCourseById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const course = await this.courseService.getCourseById(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  createCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { title, description, iconUrl, bannerImage } = req.body;

      if (!title || !description) {
        throw new CustomError('Missing required fields', 400);
      }

      const course = await this.courseService.createCourse({
        title,
        description,
        iconUrl,
        bannerImage
      });

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  updateCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const course = await this.courseService.updateCourse(id, req.body);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  deleteCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      await this.courseService.deleteCourse(id);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };
}
