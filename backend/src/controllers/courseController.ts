import { Request, Response, NextFunction } from 'express';
import { CourseService } from '../services/courseService';
import { CustomError } from '../utils/customError';

export class CourseController {
  private courseService: CourseService;

  constructor() {
    this.courseService = new CourseService();
  }

  getAllCourses = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const courses = await this.courseService.getAllCourses(req.query);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: courses
      });
    } catch (error) {
      next(error);
    }
  };

  getCourseById = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const course = await this.courseService.getCourseById(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  createCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { name, description, level, duration, iconUrl, imageUrl } = req.body;

      if (!name || !description || !level || !duration) {
        throw new CustomError('Missing required fields: name, description, level, duration', 400);
      }

      const course = await this.courseService.createCourse({
        name,
        description,
        level,
        duration,
        iconUrl,
        imageUrl
      });

      res.status(201).json({
        status: 'success',
        statusCode: 201,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  updateCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const course = await this.courseService.updateCourse(id, req.body);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  deleteCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      await this.courseService.deleteCourse(id);

      res.status(204).send();
    } catch (error) {
      next(error);
    }
  };

  publishCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const course = await this.courseService.publishCourse(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  archiveCourse = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const course = await this.courseService.archiveCourse(id);

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: course
      });
    } catch (error) {
      next(error);
    }
  };

  getCourseStats = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const stats = await this.courseService.getCourseStats();

      res.status(200).json({
        status: 'success',
        statusCode: 200,
        data: stats
      });
    } catch (error) {
      next(error);
    }
  };
}
