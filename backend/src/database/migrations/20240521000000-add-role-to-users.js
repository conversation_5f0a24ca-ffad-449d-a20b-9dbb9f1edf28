'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add role column to users table
    await queryInterface.addColumn('users', 'role', {
      type: Sequelize.ENUM('admin', 'teacher', 'student'),
      allowNull: false,
      defaultValue: 'student'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove role column from users table
    await queryInterface.removeColumn('users', 'role');
    
    // Drop the enum type (PostgreSQL specific)
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_users_role";');
  }
};
