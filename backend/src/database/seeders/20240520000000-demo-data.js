'use strict';
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create admin user
    const adminId = uuidv4();
    const userId = uuidv4();
    const adminPassword = await bcrypt.hash('admin123', 10);
    const userPassword = await bcrypt.hash('user123', 10);

    // Insert users
    await queryInterface.bulkInsert('users', [
      {
        id: adminId,
        name: 'Admin User',
        email: '<EMAIL>',
        password: adminPassword,
        role: 'admin',
        level: 10,
        xp: 5000,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: userId,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'student',
        password: userPassword,
        level: 1,
        xp: 0,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert course
    const courseId = uuidv4();
    await queryInterface.bulkInsert('courses', [
      {
        id: courseId,
        title: 'Road Signs Basics',
        description: 'Learn the fundamental road signs in Rwanda',
        icon_url: 'https://via.placeholder.com/150',
        image_url: 'https://via.placeholder.com/1200x400',
        status: 'draft',
        duration: 1,
        level: 'beginner',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert lesson
    const lessonId = uuidv4();
    await queryInterface.bulkInsert('lessons', [
      {
        id: lessonId,
        course_id: courseId,
        title: 'Warning Signs',
        content: 'Warning signs alert drivers to potential hazards ahead. They are usually yellow with black symbols.',
        thumbnail_url: 'https://via.placeholder.com/300',
        illustration_url: 'https://via.placeholder.com/800x600',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert quiz
    const quizId = uuidv4();
    await queryInterface.bulkInsert('quizzes', [
      {
        id: quizId,
        lesson_id: lessonId,
        title: 'Test Your Knowledge: Warning Signs',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert question
    const questionId = uuidv4();
    await queryInterface.bulkInsert('questions', [
      {
        id: questionId,
        quiz_id: quizId,
        text: 'What color are most warning signs in Rwanda?',
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);

    // Insert choices
    await queryInterface.bulkInsert('choices', [
      {
        id: uuidv4(),
        question_id: questionId,
        text: 'Red and white',
        is_correct: false,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        question_id: questionId,
        text: 'Yellow and black',
        is_correct: true,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        question_id: questionId,
        text: 'Green and white',
        is_correct: false,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        id: uuidv4(),
        question_id: questionId,
        text: 'Blue and white',
        is_correct: false,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    // Delete all data in reverse order
    await queryInterface.bulkDelete('choices', null, {});
    await queryInterface.bulkDelete('questions', null, {});
    await queryInterface.bulkDelete('quizzes', null, {});
    await queryInterface.bulkDelete('lessons', null, {});
    await queryInterface.bulkDelete('courses', null, {});
    await queryInterface.bulkDelete('users', null, {});
  }
};
