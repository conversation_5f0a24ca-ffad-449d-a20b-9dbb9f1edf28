'use strict';
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    console.log('🌱 Starting comprehensive data seeding...');

    // Helper function to generate random data
    const getRandomElement = (array) => array[Math.floor(Math.random() * array.length)];
    const getRandomNumber = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
    const getRandomDate = (start, end) => new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));

    // Data arrays for realistic content
    const firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'];

    con<PERSON> last<PERSON><PERSON><PERSON> = ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Niyonzima', 'Uwamahoro', 'Habimana', 'Mukamana', 'Nzeyimana', 'Uwimana', 'Bizimana', 'Mukamana', 'Nsengimana', 'Uwamahoro', 'Niyitegeka', 'Mukamana', 'Hakizimana', 'Uwimana', 'Nzeyimana', 'Mukamana', 'Bizumuremyi', 'Uwamahoro', 'Nsabimana', 'Mukamana', 'Niyonkuru', 'Uwimana', 'Hakizamungu', 'Mukamana', 'Nzeyimana', 'Uwamahoro', 'Bizimungu', 'Mukamana'];

    const courseTopics = [
      'Road Signs and Signals', 'Traffic Rules and Regulations', 'Defensive Driving Techniques', 'Vehicle Safety and Maintenance',
      'Emergency Procedures', 'Parking and Maneuvering', 'Highway Driving', 'Urban Driving Skills', 'Weather Conditions Driving',
      'Night Driving Safety', 'Motorcycle Safety', 'Commercial Vehicle Operations', 'Pedestrian Safety', 'Bicycle Traffic Rules',
      'Intersection Navigation', 'Speed Limits and Control', 'Right of Way Rules', 'Vehicle Inspection', 'Driver Health and Fitness',
      'Eco-Friendly Driving', 'Advanced Driving Techniques', 'Road Construction Zones', 'School Zone Safety', 'Emergency Vehicle Response',
      'Cargo and Load Safety', 'Passenger Transport Rules', 'Rural Road Driving', 'Mountain Driving', 'Bridge and Tunnel Safety',
      'Roundabout Navigation', 'Lane Changing Techniques', 'Overtaking Procedures', 'Backing and Reversing', 'Parallel Parking',
      'Three-Point Turns', 'Hill Start Techniques', 'Skid Control', 'Brake System Understanding', 'Steering Techniques',
      'Mirror Usage', 'Blind Spot Awareness', 'Following Distance', 'Signal Usage', 'Horn Etiquette',
      'Road Rage Prevention', 'Fatigue Management', 'Alcohol and Drug Awareness', 'Mobile Phone Restrictions', 'Seatbelt Safety'
    ];

    const lessonTopics = [
      'Understanding Warning Signs', 'Regulatory Sign Compliance', 'Information Sign Reading', 'Traffic Light Sequences',
      'Hand Signal Recognition', 'Road Marking Interpretation', 'Speed Limit Zones', 'No Entry Areas',
      'Parking Restrictions', 'Loading Zone Rules', 'School Zone Protocols', 'Construction Zone Navigation',
      'Emergency Lane Usage', 'Overtaking Zones', 'Pedestrian Crossings', 'Bicycle Lane Rules',
      'Bus Stop Regulations', 'Taxi Stand Rules', 'Disabled Parking', 'Fire Lane Restrictions',
      'Hospital Zone Quiet Areas', 'Residential Speed Limits', 'Commercial Zone Rules', 'Industrial Area Safety',
      'Tourist Area Guidelines', 'Border Crossing Procedures', 'Toll Road Usage', 'Bridge Weight Limits',
      'Tunnel Safety Protocols', 'Mountain Road Precautions', 'Wet Weather Driving', 'Fog Driving Techniques',
      'Night Visibility', 'Dawn and Dusk Driving', 'Sun Glare Management', 'Rain Driving Safety',
      'Wind Condition Handling', 'Ice and Snow Driving', 'Flood Area Navigation', 'Dust Storm Procedures'
    ];

    const quizTitles = [
      'Basic Road Signs Quiz', 'Traffic Rules Assessment', 'Safety Knowledge Test', 'Emergency Procedures Quiz',
      'Parking Skills Test', 'Highway Rules Quiz', 'Urban Driving Assessment', 'Weather Driving Test',
      'Night Driving Quiz', 'Vehicle Safety Check', 'Defensive Driving Test', 'Speed Control Quiz',
      'Right of Way Assessment', 'Intersection Rules Test', 'Signal Recognition Quiz', 'Road Marking Test',
      'Construction Zone Quiz', 'School Zone Safety Test', 'Emergency Response Quiz', 'Vehicle Maintenance Test'
    ];

    const questionTemplates = [
      'What is the meaning of this road sign?',
      'What should you do when approaching this situation?',
      'What is the correct speed limit in this area?',
      'Which action is required at this intersection?',
      'What does this road marking indicate?',
      'When is it safe to perform this maneuver?',
      'What is the proper following distance?',
      'Which signal should you use in this situation?',
      'What is the penalty for this traffic violation?',
      'How should you respond to this emergency vehicle?'
    ];

    // Create admin and sample users first
    const adminId = uuidv4();
    const adminPassword = await bcrypt.hash('admin123', 10);
    const userPassword = await bcrypt.hash('user123', 10);

    // Generate users (100+ users)
    console.log('👥 Generating users...');
    const users = [];
    const roles = ['student', 'teacher', 'admin'];
    const roleWeights = [0.8, 0.15, 0.05]; // 80% students, 15% teachers, 5% admins

    // Add the main admin user
    users.push({
      id: adminId,
      name: 'Admin User',
      email: '<EMAIL>',
      password: adminPassword,
      role: 'admin',
      level: 10,
      xp: 5000,
      created_at: new Date(),
      updated_at: new Date()
    });

    // Generate 150 additional users
    for (let i = 0; i < 150; i++) {
      const firstName = getRandomElement(firstNames);
      const lastName = getRandomElement(lastNames);
      const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${i}@example.com`;

      // Determine role based on weights
      let role = 'student';
      const rand = Math.random();
      if (rand < roleWeights[2]) {
        role = 'admin';
      } else if (rand < roleWeights[2] + roleWeights[1]) {
        role = 'teacher';
      }

      // Generate level and XP based on role
      let level, xp;
      if (role === 'admin') {
        level = getRandomNumber(8, 10);
        xp = getRandomNumber(3000, 10000);
      } else if (role === 'teacher') {
        level = getRandomNumber(5, 9);
        xp = getRandomNumber(1500, 5000);
      } else {
        level = getRandomNumber(1, 7);
        xp = getRandomNumber(0, 2500);
      }

      users.push({
        id: uuidv4(),
        name: `${firstName} ${lastName}`,
        email: email,
        password: userPassword,
        role: role,
        level: level,
        xp: xp,
        created_at: getRandomDate(new Date(2023, 0, 1), new Date()),
        updated_at: getRandomDate(new Date(2023, 6, 1), new Date())
      });
    }

    await queryInterface.bulkInsert('users', users);
    console.log(`✅ Created ${users.length} users`);

    // Store user IDs for later use
    const userIds = users.map(user => user.id);

    // Generate courses (100+ courses)
    console.log('📚 Generating courses...');
    const courses = [];
    const levels = ['beginner', 'intermediate', 'advanced'];
    const statuses = ['draft', 'published', 'archived'];
    const statusWeights = [0.2, 0.7, 0.1]; // 20% draft, 70% published, 10% archived

    for (let i = 0; i < 120; i++) {
      const topic = getRandomElement(courseTopics);
      const level = getRandomElement(levels);

      // Determine status based on weights
      let status = 'published';
      const rand = Math.random();
      if (rand < statusWeights[0]) {
        status = 'draft';
      } else if (rand > statusWeights[0] + statusWeights[1]) {
        status = 'archived';
      }

      const duration = getRandomNumber(1, 8);
      const courseNumber = String(i + 1).padStart(3, '0');

      courses.push({
        id: uuidv4(),
        title: `${topic} ${courseNumber}`,
        description: `Comprehensive course covering ${topic.toLowerCase()}. This course will teach you essential skills and knowledge needed for safe driving in Rwanda. Perfect for ${level} level drivers.`,
        icon_url: `https://via.placeholder.com/150/4F46E5/FFFFFF?text=C${courseNumber}`,
        image_url: `https://via.placeholder.com/1200x400/6366F1/FFFFFF?text=${encodeURIComponent(topic)}`,
        status: status,
        duration: duration,
        level: level,
        created_at: getRandomDate(new Date(2023, 0, 1), new Date()),
        updated_at: getRandomDate(new Date(2023, 6, 1), new Date())
      });
    }

    await queryInterface.bulkInsert('courses', courses);
    console.log(`✅ Created ${courses.length} courses`);

    // Store course IDs for lessons
    const courseIds = courses.map(course => course.id);

    // Generate lessons (300+ lessons, 2-5 per course)
    console.log('📖 Generating lessons...');
    const lessons = [];

    for (const courseId of courseIds) {
      const lessonsPerCourse = getRandomNumber(2, 5);

      for (let j = 0; j < lessonsPerCourse; j++) {
        const topic = getRandomElement(lessonTopics);
        const lessonNumber = String(j + 1).padStart(2, '0');

        lessons.push({
          id: uuidv4(),
          course_id: courseId,
          title: `Lesson ${lessonNumber}: ${topic}`,
          content: `This lesson covers ${topic.toLowerCase()} in detail. You will learn the key concepts, practical applications, and safety considerations. The content includes real-world examples and scenarios that you might encounter while driving in Rwanda. By the end of this lesson, you will have a solid understanding of ${topic.toLowerCase()} and how to apply this knowledge safely on the road.`,
          thumbnail_url: `https://via.placeholder.com/300/10B981/FFFFFF?text=L${lessonNumber}`,
          illustration_url: `https://via.placeholder.com/800x600/059669/FFFFFF?text=${encodeURIComponent(topic)}`,
          created_at: getRandomDate(new Date(2023, 0, 1), new Date()),
          updated_at: getRandomDate(new Date(2023, 6, 1), new Date())
        });
      }
    }

    await queryInterface.bulkInsert('lessons', lessons);
    console.log(`✅ Created ${lessons.length} lessons`);

    // Store lesson IDs for quizzes
    const lessonIds = lessons.map(lesson => lesson.id);

    // Generate quizzes (300+ quizzes, 1 per lesson)
    console.log('🧠 Generating quizzes...');
    const quizzes = [];

    for (const lessonId of lessonIds) {
      const quizTitle = getRandomElement(quizTitles);

      quizzes.push({
        id: uuidv4(),
        lesson_id: lessonId,
        title: quizTitle,
        created_at: getRandomDate(new Date(2023, 0, 1), new Date()),
        updated_at: getRandomDate(new Date(2023, 6, 1), new Date())
      });
    }

    await queryInterface.bulkInsert('quizzes', quizzes);
    console.log(`✅ Created ${quizzes.length} quizzes`);

    // Store quiz IDs for questions
    const quizIds = quizzes.map(quiz => quiz.id);

    // Generate questions (1000+ questions, 3-5 per quiz)
    console.log('❓ Generating questions...');
    const questions = [];

    for (const quizId of quizIds) {
      const questionsPerQuiz = getRandomNumber(3, 5);

      for (let k = 0; k < questionsPerQuiz; k++) {
        const questionText = getRandomElement(questionTemplates);
        const questionNumber = k + 1;

        questions.push({
          id: uuidv4(),
          quiz_id: quizId,
          text: `${questionNumber}. ${questionText}`,
          created_at: getRandomDate(new Date(2023, 0, 1), new Date()),
          updated_at: getRandomDate(new Date(2023, 6, 1), new Date())
        });
      }
    }

    await queryInterface.bulkInsert('questions', questions);
    console.log(`✅ Created ${questions.length} questions`);

    // Store question IDs for choices
    const questionIds = questions.map(question => question.id);

    // Generate choices (4000+ choices, 4 per question)
    console.log('✅ Generating choices...');
    const choices = [];

    const choiceTemplates = [
      ['Always stop completely', 'Slow down and proceed', 'Speed up to clear quickly', 'Ignore the sign'],
      ['Red and white', 'Yellow and black', 'Green and white', 'Blue and white'],
      ['10 km/h', '30 km/h', '50 km/h', '80 km/h'],
      ['Left turn only', 'Right turn only', 'Straight ahead only', 'No restrictions'],
      ['Parking allowed', 'No parking', 'Loading zone', 'Taxi stand'],
      ['Yield to traffic', 'Stop completely', 'Proceed with caution', 'Sound horn'],
      ['2 seconds', '3 seconds', '5 seconds', '10 seconds'],
      ['Left signal', 'Right signal', 'Hazard lights', 'No signal needed'],
      ['Fine only', 'Points deduction', 'License suspension', 'All of the above'],
      ['Pull over immediately', 'Speed up', 'Continue normally', 'Block the lane']
    ];

    for (const questionId of questionIds) {
      const choiceSet = getRandomElement(choiceTemplates);
      const correctIndex = getRandomNumber(0, 3); // Random correct answer

      for (let l = 0; l < 4; l++) {
        choices.push({
          id: uuidv4(),
          question_id: questionId,
          text: choiceSet[l],
          is_correct: l === correctIndex,
          created_at: getRandomDate(new Date(2023, 0, 1), new Date()),
          updated_at: getRandomDate(new Date(2023, 6, 1), new Date())
        });
      }
    }

    await queryInterface.bulkInsert('choices', choices);
    console.log(`✅ Created ${choices.length} choices`);

    console.log('🎉 Seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   👥 Users: ${users.length}`);
    console.log(`   📚 Courses: ${courses.length}`);
    console.log(`   📖 Lessons: ${lessons.length}`);
    console.log(`   🧠 Quizzes: ${quizzes.length}`);
    console.log(`   ❓ Questions: ${questions.length}`);
    console.log(`   ✅ Choices: ${choices.length}`);
  },

  async down(queryInterface) {
    console.log('🧹 Cleaning up seeded data...');

    // Delete all data in reverse order to respect foreign key constraints
    await queryInterface.bulkDelete('choices', null, {});
    console.log('✅ Deleted choices');

    await queryInterface.bulkDelete('questions', null, {});
    console.log('✅ Deleted questions');

    await queryInterface.bulkDelete('quizzes', null, {});
    console.log('✅ Deleted quizzes');

    await queryInterface.bulkDelete('lessons', null, {});
    console.log('✅ Deleted lessons');

    await queryInterface.bulkDelete('courses', null, {});
    console.log('✅ Deleted courses');

    await queryInterface.bulkDelete('users', null, {});
    console.log('✅ Deleted users');

    console.log('🎉 Cleanup completed successfully!');
  }
};
