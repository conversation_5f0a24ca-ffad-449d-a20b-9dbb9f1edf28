import { BaseService } from './baseService';
import { CustomError } from '../utils/customError';
import Lesson from '../models/lesson';
import Course from '../models/course';
import Quiz from '../models/quiz';
import UserProgress from '../models/userProgress';

interface CreateLessonInput {
  courseId: string;
  title: string;
  content: string;
  thumbnailUrl?: string;
  illustrationUrl?: string;
}

export class LessonService extends BaseService {
  async getLessonsByCourse(courseId: string): Promise<Lesson[]> {
    try {
      return await Lesson.findAll({
        where: { courseId },
        order: [['title', 'ASC']]
      });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getLessonById(id: string): Promise<Lesson> {
    try {
      const lesson = await Lesson.findByPk(id, {
        include: [
          {
            model: Course,
            as: 'course',
            attributes: ['id', 'title']
          },
          {
            model: Quiz,
            as: 'quizzes',
            attributes: ['id', 'title']
          }
        ]
      });

      if (!lesson) {
        throw new CustomError('Lesson not found', 404);
      }

      return lesson;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async createLesson(data: CreateLessonInput): Promise<Lesson> {
    try {
      // Verify course exists
      const courseExists = await Course.findByPk(data.courseId);

      if (!courseExists) {
        throw new CustomError('Course not found', 404);
      }

      return await Lesson.create(data);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateLesson(id: string, data: Partial<CreateLessonInput>): Promise<Lesson> {
    try {
      if (data.courseId) {
        // Verify course exists if courseId is being updated
        const courseExists = await Course.findByPk(data.courseId);

        if (!courseExists) {
          throw new CustomError('Course not found', 404);
        }
      }

      const lesson = await Lesson.findByPk(id);
      if (!lesson) {
        throw new CustomError('Lesson not found', 404);
      }

      return await lesson.update(data);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteLesson(id: string): Promise<void> {
    try {
      const lesson = await Lesson.findByPk(id);
      if (!lesson) {
        throw new CustomError('Lesson not found', 404);
      }

      await lesson.destroy();
    } catch (error) {
      this.handleError(error);
    }
  }

  async markLessonComplete(lessonId: string, userId: string): Promise<void> {
    try {
      const progress = await UserProgress.findOne({
        where: {
          userId,
          lessonId
        }
      });

      if (progress) {
        // Update existing progress
        await progress.update({
          completed: true
        });
      } else {
        // Create new progress
        await UserProgress.create({
          userId,
          lessonId,
          completed: true,
          score: 0,
          streak: 0
        });
      }
    } catch (error) {
      this.handleError(error);
    }
  }
}
