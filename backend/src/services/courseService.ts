import { BaseService } from './baseService';
import { CustomError } from '../utils/customError';
import { PaginationQuery } from '../types/api';
import Course, { CourseLevel, CourseStatus } from '../models/course';
import Lesson from '../models/lesson';
import { Op } from 'sequelize';

interface CreateCourseInput {
  title: string;
  description: string;
  level: CourseLevel;
  duration: number;
  iconUrl: string;
  imageUrl: string;
}

interface CourseFilters extends PaginationQuery {
  search?: string;
  level?: string;
  status?: string;
}

export class CourseService extends BaseService {
  async getAllCourses(query: CourseFilters): Promise<{
    courses: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const { offset, limit, page } = this.getPaginationParams(query);
      const { search, level, status } = query;

      // Build where clause for filtering
      const whereClause: any = {};

      if (search) {
        whereClause[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } }
        ];
      }

      if (level) {
        whereClause.level = level;
      }

      if (status) {
        whereClause.status = status;
      }

      const { count, rows } = await Course.findAndCountAll({
        where: whereClause,
        offset,
        limit,
        order: [['title', 'ASC']],
        include: [{
          model: Lesson,
          as: 'lessons',
          attributes: []
        }],
        attributes: {
          include: [
            [
              Course.sequelize!.fn('COUNT', Course.sequelize!.col('lessons.id')),
              'lessonsCount'
            ]
          ]
        },
        group: ['Course.id'],
        subQuery: false
      });

      // Format the response to match frontend expectations
      const courses = rows.map((course: any) => ({
        id: course.id,
        title: course.title,
        description: course.description,
        level: course.level,
        duration: course.duration,
        status: course.status,
        createdAt: course.createdAt,
        updatedAt: course.updatedAt,
        lessonsCount: parseInt(course.dataValues.lessonsCount) || 0
      }));

      const totalPages = Math.ceil(count.length / limit);

      return {
        courses,
        total: count.length,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getCourseById(id: string): Promise<Course> {
    try {
      const course = await Course.findByPk(id, {
        include: [{
          model: Lesson,
          as: 'lessons',
          attributes: ['id', 'title', 'thumbnailUrl']
        }]
      });

      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      return course;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async createCourse(data: CreateCourseInput): Promise<Course> {
    try {
      return await Course.create({
        ...data,
        status: CourseStatus.DRAFT
      });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateCourse(id: string, data: Partial<CreateCourseInput & { status?: CourseStatus }>): Promise<Course> {
    try {
      const course = await Course.findByPk(id);
      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      return await course.update(data);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteCourse(id: string): Promise<void> {
    try {
      const course = await Course.findByPk(id);
      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      await course.destroy();
    } catch (error) {
      this.handleError(error);
    }
  }

  async publishCourse(id: string): Promise<Course> {
    try {
      const course = await Course.findByPk(id);
      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      return await course.update({ status: CourseStatus.PUBLISHED });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async archiveCourse(id: string): Promise<Course> {
    try {
      const course = await Course.findByPk(id);
      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      return await course.update({ status: CourseStatus.ARCHIVED });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getCourseStats(): Promise<{
    totalCourses: number;
    publishedCourses: number;
    draftCourses: number;
    totalEnrollments: number;
  }> {
    try {
      const totalCourses = await Course.count();
      const publishedCourses = await Course.count({ where: { status: CourseStatus.PUBLISHED } });
      const draftCourses = await Course.count({ where: { status: CourseStatus.DRAFT } });

      return {
        totalCourses,
        publishedCourses,
        draftCourses,
        totalEnrollments: 0 // Will be implemented when user enrollment is added
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
}
