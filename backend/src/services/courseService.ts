import { BaseService } from './baseService';
import { CustomError } from '../utils/customError';
import { PaginationQuery } from '../types/api';
import Course from '../models/course';
import Lesson from '../models/lesson';

interface CreateCourseInput {
  title: string;
  description: string;
  iconUrl?: string;
  bannerImage?: string;
}

export class CourseService extends BaseService {
  async getAllCourses(query: PaginationQuery): Promise<{
    items: Course[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const { offset, limit, page } = this.getPaginationParams(query);

      const { count, rows } = await Course.findAndCountAll({
        offset,
        limit,
        order: [['title', 'ASC']]
      });

      return {
        items: rows,
        total: count,
        page,
        limit
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getCourseById(id: string): Promise<Course> {
    try {
      const course = await Course.findByPk(id, {
        include: [{
          model: Lesson,
          as: 'lessons',
          attributes: ['id', 'title', 'thumbnailUrl']
        }]
      });

      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      return course;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async createCourse(data: CreateCourseInput): Promise<Course> {
    try {

      return await Course.create({...data, isPublished: false });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateCourse(id: string, data: Partial<CreateCourseInput>): Promise<Course> {
    try {
      const course = await Course.findByPk(id);
      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      return await course.update(data);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteCourse(id: string): Promise<void> {
    try {
      const course = await Course.findByPk(id);
      if (!course) {
        throw new CustomError('Course not found', 404);
      }

      await course.destroy();
    } catch (error) {
      this.handleError(error);
    }
  }
}
