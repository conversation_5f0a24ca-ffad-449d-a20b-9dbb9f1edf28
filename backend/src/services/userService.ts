import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { BaseService } from './baseService';
import { config } from '../config';
import { CustomError } from '../utils/customError';
import { TokenResponse } from '../types/api';
import User, { UserRole } from '../models/user';

interface CreateUserInput {
  name: string;
  email: string;
  password: string;
  role: UserRole;
}

interface LoginInput {
  email: string;
  password: string;
}

export class UserService extends BaseService {
  async createUser({ name, email, password, role }: CreateUserInput): Promise<User> {
    try {
      const hashedPassword = await bcrypt.hash(password, config.bcryptSaltRounds);

      const user = await User.create({
        name,
        email,
        password: hashedPassword,
        role
      });

      return user;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async login({ email, password }: LoginInput): Promise<TokenResponse> {
    try {
      const user = await User.findOne({ where: { email } });

      if (!user) {
        throw new CustomError('Invalid credentials', 401);
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        throw new CustomError('Invalid credentials', 401);
      }

      // Create JWT token with role information
      const payload = { id: user.id, email: user.email, role: user.role };
      const token = jwt.sign(payload, String(config.jwtSecret));

      return {
        token,
        expiresIn: config.jwtExpiresIn
      };
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getUserById(id: string): Promise<User> {
    try {
      const user = await User.findByPk(id);

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      return user;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateUserXP(userId: string, xpToAdd: number): Promise<User> {
    try {
      const user = await User.findByPk(userId);

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      const newXP = user.xp + xpToAdd;
      const newLevel = Math.floor(newXP / 1000) + 1; // Simple leveling logic

      user.xp = newXP;
      user.level = newLevel;
      await user.save();

      return user;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getAllUsers(): Promise<User[]> {
    try {
      return await User.findAll({
        attributes: ['id', 'name', 'email', 'role', 'level', 'xp', 'createdAt', 'updatedAt']
      });
    } catch (error) {
      return this.handleError(error);
    }
  }

  async isAdmin(userId: string): Promise<boolean> {
    try {
      const user = await User.findByPk(userId, {
        attributes: ['role']
      });

      if (!user) {
        throw new CustomError('User not found', 404);
      }

      return user.role === UserRole.ADMIN;
    } catch (error) {
      return this.handleError(error);
    }
  }
}
