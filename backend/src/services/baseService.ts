import { Sequelize } from 'sequelize';
import { sequelize } from '../models';
import { CustomError } from '../utils/customError';
import { PaginationQuery } from '../types/api';

export class BaseService {
  protected sequelize: Sequelize;

  constructor() {
    this.sequelize = sequelize;
  }

  protected handleError(error: any): never {
    console.error('Service Error:', error);

    if (error instanceof CustomError) {
      throw error;
    }

    // Handle Sequelize errors
    if (error.name === 'SequelizeUniqueConstraintError') {
      throw new CustomError('Unique constraint violation', 400);
    } else if (error.name === 'SequelizeForeignKeyConstraintError') {
      throw new CustomError('Foreign key constraint violation', 400);
    } else if (error.name === 'SequelizeValidationError') {
      throw new CustomError('Validation error', 400);
    } else if (error.name === 'SequelizeEmptyResultError') {
      throw new CustomError('Record not found', 404);
    }

    throw new CustomError('Internal server error', 500);
  }

  protected getPaginationParams(query: PaginationQuery) {
    const page = Math.max(1, parseInt(String(query.page || 1)));
    const limit = Math.min(100, Math.max(1, parseInt(String(query.limit || 10))));
    const offset = (page - 1) * limit;

    return {
      offset,
      limit,
      page
    };
  }
}
