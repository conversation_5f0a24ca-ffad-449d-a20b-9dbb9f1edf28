import { BaseService } from './baseService';
import { CustomError } from '../utils/customError';
import Quiz from '../models/quiz';
import Question from '../models/question';
import Choice from '../models/choice';
import Lesson from '../models/lesson';
import User from '../models/user';
import UserProgress from '../models/userProgress';

interface CreateQuizInput {
  lessonId: string;
  title: string;
  questions: Array<{
    text: string;
    choices: Array<{
      text: string;
      isCorrect: boolean;
    }>;
  }>;
}

interface QuizSubmissionResult {
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  xpEarned: number;
}

// Define interfaces for the return types
interface ChoiceData {
  id: string;
  questionId: string;
  text: string;
  isCorrect: boolean;
}

interface QuestionWithChoices {
  id: string;
  quizId: string;
  text: string;
  choices: ChoiceData[];
  createdAt: Date;
  updatedAt: Date;
}

interface QuizWithQuestions {
  id: string;
  lessonId: string;
  title: string;
  questions: QuestionWithChoices[];
  createdAt: Date;
  updatedAt: Date;
}

export class QuizService extends BaseService {
  async getQuizByLesson(lessonId: string): Promise<QuizWithQuestions> {
    try {
      const quiz = await Quiz.findOne({
        where: { lessonId },
        include: [
          {
            model: Question,
            as: 'questions',
            include: [
              {
                model: Choice,
                as: 'choices'
              }
            ]
          }
        ]
      });

      if (!quiz) {
        throw new CustomError('Quiz not found for this lesson', 404);
      }

      // Convert to the expected return type
      const result: QuizWithQuestions = {
        id: quiz.id,
        lessonId: quiz.lessonId,
        title: quiz.title,
        createdAt: quiz.createdAt,
        updatedAt: quiz.updatedAt,
        questions: quiz.questions?.map(question => ({
          id: question.id,
          quizId: question.quizId,
          text: question.text,
          createdAt: question.createdAt,
          updatedAt: question.updatedAt,
          choices: question.choices?.map(choice => ({
            id: choice.id,
            questionId: choice.questionId,
            text: choice.text,
            isCorrect: choice.isCorrect
          })) || []
        })) || []
      };

      return result;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async getQuizById(id: string): Promise<QuizWithQuestions> {
    try {
      const quiz = await Quiz.findByPk(id, {
        include: [
          {
            model: Question,
            as: 'questions',
            include: [
              {
                model: Choice,
                as: 'choices'
              }
            ]
          }
        ]
      });

      if (!quiz) {
        throw new CustomError('Quiz not found', 404);
      }

      // Convert to the expected return type
      const result: QuizWithQuestions = {
        id: quiz.id,
        lessonId: quiz.lessonId,
        title: quiz.title,
        createdAt: quiz.createdAt,
        updatedAt: quiz.updatedAt,
        questions: quiz.questions?.map(question => ({
          id: question.id,
          quizId: question.quizId,
          text: question.text,
          createdAt: question.createdAt,
          updatedAt: question.updatedAt,
          choices: question.choices?.map(choice => ({
            id: choice.id,
            questionId: choice.questionId,
            text: choice.text,
            isCorrect: choice.isCorrect
          })) || []
        })) || []
      };

      return result;
    } catch (error) {
      return this.handleError(error);
    }
  }

  async createQuiz(data: CreateQuizInput): Promise<QuizWithQuestions> {
    try {
      // Verify lesson exists
      const lessonExists = await Lesson.findByPk(data.lessonId);

      if (!lessonExists) {
        throw new CustomError('Lesson not found', 404);
      }

      // Create quiz
      const quiz = await Quiz.create({
        lessonId: data.lessonId,
        title: data.title
      });

      // Create questions and choices
      for (const questionData of data.questions) {
        const question = await Question.create({
          quizId: quiz.id,
          text: questionData.text
        });

        // Create choices for this question
        await Choice.bulkCreate(
          questionData.choices.map(choice => ({
            questionId: question.id,
            text: choice.text,
            isCorrect: choice.isCorrect
          }))
        );
      }

      // Return the quiz with questions and choices
      return await this.getQuizById(quiz.id);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async updateQuiz(id: string, data: Partial<CreateQuizInput>): Promise<QuizWithQuestions> {
    try {
      if (data.lessonId) {
        const lessonExists = await Lesson.findByPk(data.lessonId);

        if (!lessonExists) {
          throw new CustomError('Lesson not found', 404);
        }
      }

      // Find the quiz
      const quiz = await Quiz.findByPk(id);
      if (!quiz) {
        throw new CustomError('Quiz not found', 404);
      }

      // Update basic quiz data
      await quiz.update({
        title: data.title || quiz.title,
        lessonId: data.lessonId || quiz.lessonId
      });

      // If questions are provided, update them
      if (data.questions) {
        // Find all questions for this quiz
        const questions = await Question.findAll({
          where: { quizId: id }
        });

        // Delete all choices for these questions
        for (const question of questions) {
          await Choice.destroy({
            where: { questionId: question.id }
          });
        }

        // Delete all questions
        await Question.destroy({
          where: { quizId: id }
        });

        // Create new questions and choices
        for (const questionData of data.questions) {
          const question = await Question.create({
            quizId: id,
            text: questionData.text
          });

          // Create choices for this question
          await Choice.bulkCreate(
            questionData.choices.map(choice => ({
              questionId: question.id,
              text: choice.text,
              isCorrect: choice.isCorrect
            }))
          );
        }
      }

      // Return the updated quiz with questions and choices
      return await this.getQuizById(id);
    } catch (error) {
      return this.handleError(error);
    }
  }

  async deleteQuiz(id: string): Promise<void> {
    try {
      const quiz = await Quiz.findByPk(id);
      if (!quiz) {
        throw new CustomError('Quiz not found', 404);
      }

      // Find all questions for this quiz
      const questions = await Question.findAll({
        where: { quizId: id }
      });

      // Delete all choices for these questions
      for (const question of questions) {
        await Choice.destroy({
          where: { questionId: question.id }
        });
      }

      // Delete all questions
      await Question.destroy({
        where: { quizId: id }
      });

      // Delete the quiz
      await quiz.destroy();
    } catch (error) {
      this.handleError(error);
    }
  }

  async submitQuiz(
    quizId: string,
    userId: string,
    answers: Record<string, string>
  ): Promise<QuizSubmissionResult> {
    try {
      const quiz = await this.getQuizById(quizId);
      let correctAnswers = 0;

      // Check each answer
      for (const question of quiz.questions) {
        const submittedAnswerId = answers[question.id];
        const correctChoice = question.choices.find((choice) => choice.isCorrect);

        if (correctChoice && submittedAnswerId === correctChoice.id) {
          correctAnswers++;
        }
      }

      const totalQuestions = quiz.questions.length;
      const score = Math.round((correctAnswers / totalQuestions) * 100);
      const xpEarned = Math.round((correctAnswers / totalQuestions) * 100); // Base XP calculation

      // Get the lesson ID from the quiz
      const quizDetails = await Quiz.findByPk(quizId, {
        attributes: ['lessonId']
      });

      if (!quizDetails) {
        throw new CustomError('Quiz not found', 404);
      }

      // Check if user progress exists
      const userProgress = await UserProgress.findOne({
        where: {
          userId,
          lessonId: quizDetails.lessonId
        }
      });

      if (userProgress) {
        // Update existing progress
        await userProgress.update({
          score: score,
          completed: score >= 70, // Mark as completed if score is 70% or higher
          streak: userProgress.streak + (score >= 70 ? 1 : 0)
        });
      } else {
        // Create new progress
        await UserProgress.create({
          userId,
          lessonId: quizDetails.lessonId,
          score: score,
          completed: score >= 70,
          streak: score >= 70 ? 1 : 0
        });
      }

      // Update user XP
      const user = await User.findByPk(userId);
      if (!user) {
        throw new CustomError('User not found', 404);
      }

      await user.update({
        xp: user.xp + xpEarned
      });

      return {
        score,
        totalQuestions,
        correctAnswers,
        xpEarned
      };
    } catch (error) {
      return this.handleError(error);
    }
  }
}
