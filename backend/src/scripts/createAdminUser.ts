import bcrypt from 'bcryptjs';
import { config } from '../config';
import User, { UserRole } from '../models/user';
import sequelize from '../config/database';

/**
 * <PERSON>ript to create an initial admin user
 * Run with: npx ts-node src/scripts/createAdminUser.ts
 */
async function createAdminUser() {
  try {
    // Connect to the database
    await sequelize.authenticate();
    console.log('Connected to the database');

    // Check if admin user already exists
    const existingAdmin = await User.findOne({
      where: { role: UserRole.ADMIN }
    });

    if (existingAdmin) {
      console.log('Admin user already exists');
      return;
    }

    // Create admin user with configurable name, email and password
    const adminName = process.env.ADMIN_NAME || 'Admin User';
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminPassword = process.env.ADMIN_PASSWORD || 'admin123';
    const hashedPassword = await bcrypt.hash(adminPassword, config.bcryptSaltRounds);

    const admin = await User.create({
      name: adminName,
      email: adminEmail,
      password: hashedPassword,
      role: UserRole.ADMIN
    });

    console.log(`Admin user created with ID: ${admin.id}`);
    console.log(`Name: ${adminName}`);
    console.log(`Email: ${adminEmail}`);
    console.log(`Password: ${adminPassword}`);
    console.log('Please change the password after first login');
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
}

// Run the function
createAdminUser();
