import { Router } from 'express';
import { authenticate } from '../middleware/authenticate';
import { CourseController } from '../controllers/courseController';

const router = Router();
const courseController = new CourseController();

// Public routes
router.get('/', courseController.getAllCourses);
router.get('/:id', courseController.getCourseById);

// Protected routes
router.use(authenticate);
router.post('/', courseController.createCourse);
router.put('/:id', courseController.updateCourse);
router.delete('/:id', courseController.deleteCourse);

export { router as courseRoutes };
