import { Router } from 'express';
import { authenticate } from '../middleware/authenticate';
import { QuizController } from '../controllers/quizController';

const router = Router();
const quizController = new QuizController();

// Public routes
router.get('/lesson/:lessonId/quiz', quizController.getQuizByLesson);
router.get('/:id', quizController.getQuizById);

// Protected routes
router.use(authenticate);
router.post('/', quizController.createQuiz);
router.put('/:id', quizController.updateQuiz);
router.delete('/:id', quizController.deleteQuiz);
router.post('/:id/submit', quizController.submitQuiz);

export { router as quizRoutes };
