import { Router } from 'express';
import { UserController } from '../controllers/userController';
import { authenticate } from '../middleware/authenticate';
import { adminAuth } from '../middleware/adminAuth';

const router = Router();
const userController = new UserController();

// Public routes
router.post('/login', userController.login);

// Protected routes - require authentication
router.get('/profile', authenticate, userController.getProfile);

// Admin-only routes - require admin privileges
router.post('/create', authenticate, adminAuth, userController.createUser);
router.get('/all', authenticate, adminAuth, userController.getAllUsers);

export { router as userRoutes };
