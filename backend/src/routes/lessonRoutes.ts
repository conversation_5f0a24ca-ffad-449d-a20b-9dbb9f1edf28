import { Router } from 'express';
import { authenticate } from '../middleware/authenticate';
import { LessonController } from '../controllers/lessonController';

const router = Router();
const lessonController = new LessonController();

// Public routes
router.get('/course/:courseId/lessons', lessonController.getLessonsByCourse);
router.get('/:id', lessonController.getLessonById);

// Protected routes
router.use(authenticate);
router.post('/', lessonController.createLesson);
router.put('/:id', lessonController.updateLesson);
router.delete('/:id', lessonController.deleteLesson);

export { router as lessonRoutes };
