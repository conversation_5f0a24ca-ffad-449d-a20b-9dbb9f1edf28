import express, { Express } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';

// Configuration
import { config } from './config';

// Middleware
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';

// Routes
import { userRoutes } from './routes/userRoutes';
import { courseRoutes } from './routes/courseRoutes';
import { lessonRoutes } from './routes/lessonRoutes';
import { quizRoutes } from './routes/quizRoutes';

// Database
import { sequelize } from './models';

async function startServer() {
  const app: Express = express();

  // Security Middleware
  app.use(helmet());
  app.use(cors({
    origin: config.cors.origin,
    credentials: true
  }));
  app.use(express.json());
  app.use(morgan(config.nodeEnv === 'development' ? 'dev' : 'combined'));

  // Health check endpoint
  app.get('/health', (_, res) => res.status(200).json({ status: 'ok' }));

  // API Routes
  app.use('/api/users', userRoutes);
  app.use('/api/courses', courseRoutes);
  app.use('/api/lessons', lessonRoutes);
  app.use('/api/quizzes', quizRoutes);

  // Error Handling
  app.use(notFoundHandler);
  app.use(errorHandler);

  // Start the server regardless of database connection
  const PORT = config.port;
  app.listen(PORT, () => {
    console.log(`
🚀 Server is running on port ${PORT}
📚 Environment: ${config.nodeEnv}
🔗 API URL: http://localhost:${PORT}
    `);
  });

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Sync models with database (only in development)
    if (config.nodeEnv === 'development') {
      await sequelize.sync({ alter: true });
      console.log('✅ Database models synchronized');
    }
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    console.log('⚠️ Server is running but database functionality will not work');
    console.log('⚠️ Please make sure your PostgreSQL database is running and the credentials are correct');
    console.log('⚠️ You can update the database configuration in the .env file');
  }
}

startServer().catch(console.error);
