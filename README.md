# Rwanda Road Rules Learning App

A gamified learning platform for teaching Rwandan traffic rules and regulations, inspired by applications like Duolingo.

## Features

- Modular courses and lessons
- Engaging quizzes with multimedia
- Gamification mechanics: XP, streaks, badges
- Admin dashboard for content management

## Tech Stack

- **Frontend**: Vue 3, Vite, TypeScript, PrimeVue, Pinia
- **Backend**: Node.js, Express.js, TypeScript
- **Database**: PostgreSQL with Sequelize ORM
- **Authentication**: JWT
- **Containerization**: Docker & Docker Compose

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- Docker and Docker Compose (for containerized setup)
- PostgreSQL (if running locally without Docker)

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd rwanda-road-rules-app
   ```

2. Install dependencies:
   ```bash
   # Backend dependencies
   cd backend
   npm install

   # Frontend dependencies
   cd ../frontend
   npm install
   ```

3. Set up environment variables:
   - Copy `.env.example` to `.env` (if not already present)
   - Update the values as needed

4. Set up the database:
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

### Running with Docker

The easiest way to run the application is using Docker Compose:

```bash
docker-compose up
```

This will start both the API server and PostgreSQL database.

### Running the Frontend

The frontend is a separate Vue.js application. To run it:

```bash
cd frontend
npm run dev
```

The frontend will be available at http://localhost:5173

#### Admin Access

To access the admin panel, navigate to http://localhost:5173/admin/login

### Running Locally

#### Option 1: With Docker Database

1. Start just the PostgreSQL database with Docker:
   ```bash
   docker-compose up -d db
   ```

2. Copy the local environment file:
   ```bash
   cp backend/.env.local backend/.env
   ```

3. Start the backend server:
   ```bash
   cd backend
   npm run dev
   ```

4. The API will be available at http://localhost:4000

#### Option 2: Fully Local

1. Install PostgreSQL on your machine
2. Create a database named `rwanda_db`
3. Update the `.env` file with your database credentials
4. Run migrations and seed the database:
   ```bash
   cd backend
   npm run db:migrate
   npm run db:seed
   ```
5. Start the backend server:
   ```bash
   npm run dev
   ```
6. The API will be available at http://localhost:4000

## API Endpoints

### Users
- `POST /api/users/register` - Register a new user
- `POST /api/users/login` - Login and get JWT token
- `GET /api/users/profile` - Get user profile (protected)

### Courses
- `GET /api/courses` - Get all courses
- `GET /api/courses/:id` - Get a specific course
- `POST /api/courses` - Create a new course (admin only)
- `PUT /api/courses/:id` - Update a course (admin only)
- `DELETE /api/courses/:id` - Delete a course (admin only)

### Lessons
- `GET /api/lessons` - Get all lessons
- `GET /api/lessons/:id` - Get a specific lesson
- `POST /api/lessons` - Create a new lesson (admin only)
- `PUT /api/lessons/:id` - Update a lesson (admin only)
- `DELETE /api/lessons/:id` - Delete a lesson (admin only)

### Quizzes
- `GET /api/quizzes/:id` - Get a specific quiz
- `POST /api/quizzes/:id/submit` - Submit quiz answers

## Development

### Database Migrations

To create and apply database migrations:

```bash
# Create a new migration
npx sequelize-cli migration:generate --name <migration-name>

# Apply migrations
npm run db:migrate
```

### Running Tests

```bash
npm test
```

## License

This project is licensed under the ISC License.
