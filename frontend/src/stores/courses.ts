import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { 
  CourseService, 
  type Course, 
  type CreateCourseRequest, 
  type UpdateCourseRequest,
  type CourseFilters,
  type CoursesResponse 
} from '@/features/courses/services/courseService';

export const useCoursesStore = defineStore('courses', () => {
  // State
  const courses = ref<Course[]>([]);
  const currentCourse = ref<Course | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const pagination = ref({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 0
  });

  // Filters
  const filters = ref<CourseFilters>({
    search: '',
    level: '',
    status: '',
    page: 1,
    limit: 10
  });

  // Computed
  const publishedCourses = computed(() => 
    courses.value.filter(course => course.status === 'published')
  );

  const draftCourses = computed(() => 
    courses.value.filter(course => course.status === 'draft')
  );

  const archivedCourses = computed(() => 
    courses.value.filter(course => course.status === 'archived')
  );

  const totalCourses = computed(() => courses.value.length);

  // Actions
  const fetchCourses = async (newFilters?: Partial<CourseFilters>) => {
    try {
      isLoading.value = true;
      error.value = null;

      // Update filters if provided
      if (newFilters) {
        Object.assign(filters.value, newFilters);
      }

      const response: CoursesResponse = await CourseService.getCourses(filters.value);
      
      courses.value = response.courses;
      pagination.value = {
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      };
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch courses';
      console.error('Error fetching courses:', err);
    } finally {
      isLoading.value = false;
    }
  };

  const fetchCourseById = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      const course = await CourseService.getCourseById(id);
      currentCourse.value = course;
      
      return course;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch course';
      console.error('Error fetching course:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createCourse = async (courseData: CreateCourseRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const newCourse = await CourseService.createCourse(courseData);
      newCourse.lessonsCount = 0;
      
      courses.value.unshift(newCourse);
      pagination.value.total += 1;

      return newCourse;
    } catch (err: any) {
      error.value = err.message || 'Failed to create course';
      console.error('Error creating course:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateCourse = async (id: string, courseData: UpdateCourseRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const updatedCourse = await CourseService.updateCourse(id, courseData);
      
      // Update in courses array
      const index = courses.value.findIndex(course => course.id === id);
      if (index !== -1) {
        courses.value[index] = updatedCourse;
      }

      // Update current course if it's the same
      if (currentCourse.value?.id === id) {
        currentCourse.value = updatedCourse;
      }

      return updatedCourse;
    } catch (err: any) {
      error.value = err.message || 'Failed to update course';
      console.error('Error updating course:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteCourse = async (id: string) => {
    try {
      isLoading.value = true;
      error.value = null;

      await CourseService.deleteCourse(id);
      
      // Remove from courses array
      courses.value = courses.value.filter(course => course.id !== id);
      pagination.value.total -= 1;

      // Clear current course if it's the deleted one
      if (currentCourse.value?.id === id) {
        currentCourse.value = null;
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to delete course';
      console.error('Error deleting course:', err);
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const publishCourse = async (id: string) => {
    try {
      const updatedCourse = await CourseService.publishCourse(id);
      
      // Update in courses array
      const index = courses.value.findIndex(course => course.id === id);
      if (index !== -1) {
        courses.value[index] = updatedCourse;
      }

      return updatedCourse;
    } catch (err: any) {
      error.value = err.message || 'Failed to publish course';
      console.error('Error publishing course:', err);
      throw err;
    }
  };

  const archiveCourse = async (id: string) => {
    try {
      const updatedCourse = await CourseService.archiveCourse(id);
      
      // Update in courses array
      const index = courses.value.findIndex(course => course.id === id);
      if (index !== -1) {
        courses.value[index] = updatedCourse;
      }

      return updatedCourse;
    } catch (err: any) {
      error.value = err.message || 'Failed to archive course';
      console.error('Error archiving course:', err);
      throw err;
    }
  };

  const searchCourses = async (searchTerm: string) => {
    await fetchCourses({ search: searchTerm, page: 1 });
  };

  const filterByLevel = async (level: string) => {
    await fetchCourses({ level, page: 1 });
  };

  const filterByStatus = async (status: string) => {
    await fetchCourses({ status, page: 1 });
  };

  const changePage = async (page: number) => {
    await fetchCourses({ page });
  };

  const clearError = () => {
    error.value = null;
  };

  const clearCurrentCourse = () => {
    currentCourse.value = null;
  };

  const resetFilters = async () => {
    filters.value = {
      search: '',
      level: '',
      status: '',
      page: 1,
      limit: 10
    };
    await fetchCourses();
  };

  return {
    // State
    courses,
    currentCourse,
    isLoading,
    error,
    pagination,
    filters,
    
    // Computed
    publishedCourses,
    draftCourses,
    archivedCourses,
    totalCourses,
    
    // Actions
    fetchCourses,
    fetchCourseById,
    createCourse,
    updateCourse,
    deleteCourse,
    publishCourse,
    archiveCourse,
    searchCourses,
    filterByLevel,
    filterByStatus,
    changePage,
    clearError,
    clearCurrentCourse,
    resetFilters
  };
});
