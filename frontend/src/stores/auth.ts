import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from 'axios';

// Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  data: {
    token: string;
    expiresIn: string;
  };
  status: string;
  statusCode: number;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'teacher';
  level: number;
  xp: number;
}

// API client
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000/api'
});

// Request interceptor for adding auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for handling errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      // Redirect to login if not already there
      if (window.location.pathname !== '/admin/login') {
        window.location.href = '/admin/login?redirect=' + window.location.pathname;
      }
    }
    return Promise.reject(error);
  }
);

// Auth API
const authApi = {
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/users/login', data);
    return response.data;
  },
  
  getProfile: async (): Promise<UserProfile> => {
    const response = await api.get<UserProfile>('/users/profile');
    return response.data;
  },
  
  logout: async (): Promise<void> => {
    localStorage.removeItem('token');
  }
};

// Auth Store
export const useAuthStore = defineStore('auth', () => {
  const user = ref<UserProfile | null>(null);
  const token = ref<string | null>(localStorage.getItem('token'));
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const isAuthenticated = computed(() => !!token.value && !!user.value);
  const isAdmin = computed(() => user.value?.role === 'admin');

  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true;
      error.value = null;
      
      const response = await authApi.login(credentials);
      token.value = response.data.token;
      localStorage.setItem('token', response.data.token);
      
      // Get user profile after successful login
      await fetchProfile();
      
      return response;
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Login failed';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchProfile = async () => {
    try {
      if (!token.value) return;
      
      const profile = await authApi.getProfile();
      user.value = profile;
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch profile';
      logout();
    }
  };

  const logout = async () => {
    await authApi.logout();
    user.value = null;
    token.value = null;
    error.value = null;
  };

  const clearError = () => {
    error.value = null;
  };

  // Initialize auth state on store creation
  if (token.value) {
    fetchProfile();
  }

  return {
    user,
    token,
    isLoading,
    error,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    fetchProfile,
    clearError,
  };
});
