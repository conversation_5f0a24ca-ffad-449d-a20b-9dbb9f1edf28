import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import {
  AuthService,
  type LoginRequest,
  type LoginResponse,
  type UserProfile
} from '@/features/auth/services/authService';

export const useAuthStore = defineStore('auth', () => {
  const user = ref<UserProfile | null>(null);
  const token = ref<string | null>(localStorage.getItem('token'));
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  const isAuthenticated = computed(() => !!token.value && !!user.value);
  const isAdmin = computed(() => user.value?.role === 'admin');

  const login = async (credentials: LoginRequest) => {
    try {
      isLoading.value = true;
      error.value = null;

      const response = await AuthService.login(credentials);
      token.value = response.token;
      localStorage.setItem('token', response.token);

      await fetchProfile();

      return response;
    } catch (err: any) {
      error.value = err.message || 'Login failed';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchProfile = async () => {
    try {
      if (!token.value) return;

      const profile = await AuthService.getProfile();
      user.value = profile;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch profile';
      logout();
    }
  };

  const logout = async () => {
    await AuthService.logout();
    user.value = null;
    token.value = null;
    error.value = null;
  };

  const clearError = () => {
    error.value = null;
  };

  // Initialize auth state on store creation
  if (token.value) {
    fetchProfile();
  }

  return {
    user,
    token,
    isLoading,
    error,
    isAuthenticated,
    isAdmin,
    login,
    logout,
    fetchProfile,
    clearError,
  };
});
