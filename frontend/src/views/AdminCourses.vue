<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText v-model="searchKey" placeholder="Search courses..." class="w-full pl-10" />
        </IconField>
      </template>
      <template #end>
        <p>Filter and sort actions will go here</p>
      </template>
    </Menubar>
    <Divider />
    <DataTable
      :value="coursesStore.courses"
      :loading="coursesStore.isLoading"
      paginator
      :rows="coursesStore.pagination.limit"
      :totalRecords="coursesStore.pagination.total"
      :rowsPerPageOptions="[5, 10, 20, 50]"
      :first="(coursesStore.pagination.page - 1) * coursesStore.pagination.limit"
      @page="onPageChange"
      tableStyle="min-width: 50rem"
      :lazy="true"
      dataKey="id"
      responsiveLayout="scroll"
    >
      <Column field="title" header="Course Name" style="width: 25%"></Column>
      <Column field="lessonsCount" header="Lessons" style="width: 15%"></Column>
      <Column field="duration" header="Duration (hrs)" style="width: 15%"></Column>
      <Column field="level" header="Level" style="width: 15%">
        <template #body="{ data }">
          <Badge
            :value="data.level"
            :severity="data.level === 'beginner' ? 'success' : data.level === 'intermediate' ? 'warning' : 'danger'"
          />
        </template>
      </Column>
      <Column field="status" header="Status" style="width: 15%">
        <template #body="{ data }">
          <Badge
            :value="data.status"
            :severity="data.status === 'published' ? 'success' : data.status === 'draft' ? 'warning' : 'secondary'"
          />
        </template>
      </Column>
      <Column header="Actions" style="width: 25%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button
              icon="pi pi-eye"
              size="small"
              severity="info"
              outlined
              @click="viewCourse(data.id)"
              v-tooltip.top="'View Course'"
            />
            <Button
              icon="pi pi-pencil"
              size="small"
              severity="secondary"
              outlined
              @click="editCourse(data.id)"
              v-tooltip.top="'Edit Course'"
            />
            <Button
              icon="pi pi-trash"
              size="small"
              severity="danger"
              outlined
              @click="deleteCourse(data.id)"
              v-tooltip.top="'Delete Course'"
            />
          </div>
        </template>
      </Column>
    </DataTable>

    <!-- Floating Action Button -->
    <Button
      icon="pi pi-plus"
      class="fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-shadow z-50"
      @click="showCreateDialog = true"
      v-tooltip.left="'Create New Course'"
      aria-label="Create New Course"
    />

    <!-- Create Course Dialog -->
    <Dialog
      v-model:visible="showCreateDialog"
      modal
      header="Create New Course"
      :style="{ width: '50rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }"
      :draggable="false"
      :closable="true"
    >
      <form @submit.prevent="createCourse" class="space-y-6">
        <div class="grid grid-cols-1 gap-6">
          <div>
            <label for="courseName" class="block text-sm font-medium mb-2">Course Name *</label>
            <InputText
              id="courseName"
              v-model="newCourse.name"
              class="w-full"
              placeholder="Enter course name"
              :invalid="!!formErrors.name"
              required
            />
            <small v-if="formErrors.name" class="text-red-500">{{ formErrors.name }}</small>
          </div>

          <div>
            <label for="courseDescription" class="block text-sm font-medium mb-2">Description *</label>
            <Textarea
              id="courseDescription"
              v-model="newCourse.description"
              class="w-full"
              rows="4"
              placeholder="Enter course description"
              :invalid="!!formErrors.description"
              required
            />
            <small v-if="formErrors.description" class="text-red-500">{{ formErrors.description }}</small>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="courseLevel" class="block text-sm font-medium mb-2">Level *</label>
              <Dropdown
                id="courseLevel"
                v-model="newCourse.level"
                :options="levelOptions"
                option-label="label"
                option-value="value"
                class="w-full"
                placeholder="Select level"
                :invalid="!!formErrors.level"
                required
              />
              <small v-if="formErrors.level" class="text-red-500">{{ formErrors.level }}</small>
            </div>

            <div>
              <label for="courseDuration" class="block text-sm font-medium mb-2">Duration (hours) *</label>
              <InputNumber
                id="courseDuration"
                v-model="newCourse.duration"
                class="w-full"
                :min="1"
                :max="100"
                placeholder="Enter duration"
                :invalid="!!formErrors.duration"
                required
              />
              <small v-if="formErrors.duration" class="text-red-500">{{ formErrors.duration }}</small>
            </div>
          </div>
        </div>

        <div class="flex justify-end gap-3 pt-4 border-t">
          <Button
            type="button"
            label="Cancel"
            severity="secondary"
            outlined
            @click="cancelCreate"
          />
          <Button
            type="submit"
            label="Create Course"
            :loading="isCreating"
            :disabled="isCreating"
          />
        </div>
      </form>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useCoursesStore } from '@/stores/courses';
import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Badge from 'primevue/badge';
import Dialog from 'primevue/dialog';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import InputNumber from 'primevue/inputnumber';

const coursesStore = useCoursesStore();
const searchKey = ref("");

// Create course dialog state
const showCreateDialog = ref(false);
const isCreating = ref(false);

// Form data
const newCourse = ref({
  name: '',
  description: '',
  level: '',
  duration: null as number | null
});

// Form validation errors
const formErrors = ref({
  name: '',
  description: '',
  level: '',
  duration: ''
});

// Level options for dropdown
const levelOptions = [
  { label: 'Beginner', value: 'beginner' },
  { label: 'Intermediate', value: 'intermediate' },
  { label: 'Advanced', value: 'advanced' }
];

let searchTimeout: number;
watch(searchKey, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    coursesStore.searchCourses(newValue);
  }, 300);
});

const onPageChange = (event: any) => {
  const newPage = event.page + 1;
  const newLimit = event.rows;

  coursesStore.fetchCourses({
    page: newPage,
    limit: newLimit
  });
};

const viewCourse = (courseId: string) => {
  console.log('View course:', courseId);
};

const editCourse = (courseId: string) => {
  console.log('Edit course:', courseId);
  // TODO: Open edit dialog or navigate to edit page
};

const deleteCourse = async (courseId: string) => {
  console.log('Delete course:', courseId);
  // TODO: Show confirmation dialog and delete course
  try {
    await coursesStore.deleteCourse(courseId);
    // Course will be automatically removed from the list by the store
  } catch (error) {
    console.error('Failed to delete course:', error);
    // TODO: Show error message to user
  }
};

// Form validation
const validateForm = (): boolean => {
  formErrors.value = {
    name: '',
    description: '',
    level: '',
    duration: ''
  };

  let isValid = true;

  if (!newCourse.value.name.trim()) {
    formErrors.value.name = 'Course name is required';
    isValid = false;
  }

  if (!newCourse.value.description.trim()) {
    formErrors.value.description = 'Course description is required';
    isValid = false;
  }

  if (!newCourse.value.level) {
    formErrors.value.level = 'Course level is required';
    isValid = false;
  }

  if (!newCourse.value.duration || newCourse.value.duration < 1) {
    formErrors.value.duration = 'Duration must be at least 1 hour';
    isValid = false;
  }

  return isValid;
};

// Create course handler
const createCourse = async () => {
  if (!validateForm()) {
    return;
  }

  isCreating.value = true;

  try {
    await coursesStore.createCourse({
      name: newCourse.value.name,
      description: newCourse.value.description,
      level: newCourse.value.level as 'beginner' | 'intermediate' | 'advanced',
      duration: newCourse.value.duration!
    });

    // Reset form and close dialog
    resetForm();
    showCreateDialog.value = false;

  } catch (error) {
    console.error('Error creating course:', error);
    // TODO: Show error message to user
  } finally {
    isCreating.value = false;
  }
};

// Cancel create handler
const cancelCreate = () => {
  resetForm();
  showCreateDialog.value = false;
};

// Reset form data
const resetForm = () => {
  newCourse.value = {
    name: '',
    description: '',
    level: '',
    duration: null
  };
  formErrors.value = {
    name: '',
    description: '',
    level: '',
    duration: ''
  };
};

onMounted(() => {
  coursesStore.fetchCourses();
});
</script>
