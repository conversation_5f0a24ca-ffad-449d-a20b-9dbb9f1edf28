<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <MenuBar>
      <template #start>
        <InputText type="search" v-model="searchKey" />
      </template>
    </MenuBar>

    <Dialog v-model:visible="showCreateDialog" modal header="Create New Course" :style="{ width: '50rem' }"
      :breakpoints="{ '1199px': '75vw', '575px': '90vw' }">
      <form @submit.prevent="createCourse" class="space-y-6">
        <div>
          <label for="courseName" class="block text-sm font-medium mb-2">Course Name</label>
          <InputText id="courseName" v-model="newCourse.name" class="w-full" placeholder="Enter course name" required />
        </div>

        <div>
          <label for="courseDescription" class="block text-sm font-medium mb-2">Description</label>
          <Textarea id="courseDescription" v-model="newCourse.description" class="w-full" rows="4"
            placeholder="Enter course description" required />
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="courseLevel" class="block text-sm font-medium mb-2">Level</label>
            <Dropdown id="courseLevel" v-model="newCourse.level" :options="levelOptions" option-label="label"
              option-value="value" class="w-full" placeholder="Select level" required />
          </div>

          <div>
            <label for="courseDuration" class="block text-sm font-medium mb-2">Duration (hours)</label>
            <InputNumber id="courseDuration" v-model="newCourse.duration" class="w-full" :min="1" :max="100"
              placeholder="Enter duration" required />
          </div>
        </div>

        <div class="flex justify-end gap-3 pt-4">
          <Button type="button" label="Cancel" severity="secondary" @click="showCreateDialog = false" />
          <Button type="submit" label="Create Course" :loading="isCreating" />
        </div>
      </form>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { Divider } from 'primevue'
import Button from 'primevue/button';
import Card from 'primevue/card';
import Badge from 'primevue/badge';
import Dialog from 'primevue/dialog';
import InputText from 'primevue/inputtext';
import Textarea from 'primevue/textarea';
import Dropdown from 'primevue/dropdown';
import InputNumber from 'primevue/inputnumber';

// Reactive data
const showCreateDialog = ref(false);
const isCreating = ref(false);

const newCourse = reactive({
  name: '',
  description: '',
  level: '',
  duration: null
});

const levelOptions = [
  { label: 'Beginner', value: 'beginner' },
  { label: 'Intermediate', value: 'intermediate' },
  { label: 'Advanced', value: 'advanced' }
];

// Methods
const createCourse = async () => {
  isCreating.value = true;

  try {
    // TODO: Implement API call to create course
    console.log('Creating course:', newCourse);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Reset form
    Object.assign(newCourse, {
      name: '',
      description: '',
      level: '',
      duration: null
    });

    showCreateDialog.value = false;

    // TODO: Refresh courses list

  } catch (error) {
    console.error('Error creating course:', error);
  } finally {
    isCreating.value = false;
  }
};

const editCourse = (courseId: number) => {
  console.log('Edit course:', courseId);
  // TODO: Implement edit functionality
};

const deleteCourse = (courseId: number) => {
  console.log('Delete course:', courseId);
  // TODO: Implement delete functionality with confirmation
};
</script>
