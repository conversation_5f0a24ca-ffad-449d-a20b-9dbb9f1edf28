<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText v-model="searchKey" placeholder="Search courses..." class="w-full pl-10" />
        </IconField>
      </template>
      <template #end>
        <p>Filter and sort actions will go here</p>
      </template>
    </Menubar>
    <Divider />
    <DataTable
      :value="coursesStore.courses"
      :loading="coursesStore.isLoading"
      paginator
      :rows="10"
      :rowsPerPageOptions="[5, 10, 20, 50]"
      tableStyle="min-width: 50rem"
    >
      <Column field="name" header="Course Name" style="width: 30%"></Column>
      <Column field="level" header="Level" style="width: 15%">
        <template #body="{ data }">
          <Badge
            :value="data.level"
            :severity="data.level === 'beginner' ? 'success' : data.level === 'intermediate' ? 'warning' : 'danger'"
          />
        </template>
      </Column>
      <Column field="duration" header="Duration (hrs)" style="width: 15%"></Column>
      <Column field="status" header="Status" style="width: 15%">
        <template #body="{ data }">
          <Badge
            :value="data.status"
            :severity="data.status === 'published' ? 'success' : data.status === 'draft' ? 'warning' : 'secondary'"
          />
        </template>
      </Column>
      <Column header="Actions" style="width: 25%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button icon="pi pi-eye" size="small" severity="info" outlined />
            <Button icon="pi pi-pencil" size="small" severity="secondary" outlined />
            <Button icon="pi pi-trash" size="small" severity="danger" outlined />
          </div>
        </template>
      </Column>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useCoursesStore } from '@/stores/courses';
import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Badge from 'primevue/badge';

const coursesStore = useCoursesStore();
const searchKey = ref("");

// Watch for search input changes and debounce the search
let searchTimeout: number;
watch(searchKey, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    coursesStore.searchCourses(newValue);
  }, 300);
});

// Fetch courses on component mount
onMounted(() => {
  coursesStore.fetchCourses();
});
</script>
