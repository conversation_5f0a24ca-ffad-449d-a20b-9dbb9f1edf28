<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText v-model="searchKey" placeholder="Search courses..." class="w-full pl-10" />
        </IconField>
      </template>
      <template #end>
        <p>Filter and sort actions will go here</p>
      </template>
    </Menubar>
    <Divider />
    <DataTable :value="[]" paginator :rows="5" :rowsPerPageOptions="[5, 10, 20, 50]"
      tableStyle="min-width: 50rem">
      <Column field="title" header="Title" style="width: 25%"></Column>
      <Column field="iconUrl" header="Icon Url" style="width: 25%"></Column>
      <Column field="bannerImage" header="Banner Url" style="width: 25%"></Column>
      <Column field="isPublished" header="Status" style="width: 25%"></Column>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';

const searchKey = ref("")
</script>
