<template>
  <div class="p-3">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Courses Management
        </h1>
        <p class="text-surface-600 dark:text-surface-400">
          Create and manage learning courses
        </p>
      </div>
    </div>
    <Divider />
    <Menubar>
      <template #start>
        <IconField>
          <InputIcon class="pi pi-search" />
          <InputText v-model="searchKey" placeholder="Search courses..." class="w-full pl-10" />
        </IconField>
      </template>
      <template #end>
        <p>Filter and sort actions will go here</p>
      </template>
    </Menubar>
    <Divider />
    <DataTable
      :value="coursesStore.courses"
      :loading="coursesStore.isLoading"
      paginator
      :rows="coursesStore.pagination.limit"
      :totalRecords="coursesStore.pagination.total"
      :rowsPerPageOptions="[5, 10, 20, 50]"
      :first="(coursesStore.pagination.page - 1) * coursesStore.pagination.limit"
      @page="onPageChange"
      tableStyle="min-width: 50rem"
      :lazy="true"
      dataKey="id"
      responsiveLayout="scroll"
    >
      <Column field="title" header="Course Name" style="width: 25%"></Column>
      <Column field="lessonsCount" header="Lessons" style="width: 15%"></Column>
      <Column field="duration" header="Duration (hrs)" style="width: 15%"></Column>
      <Column field="level" header="Level" style="width: 15%">
        <template #body="{ data }">
          <Badge
            :value="data.level"
            :severity="data.level === 'beginner' ? 'success' : data.level === 'intermediate' ? 'warning' : 'danger'"
          />
        </template>
      </Column>
      <Column field="status" header="Status" style="width: 15%">
        <template #body="{ data }">
          <Badge
            :value="data.status"
            :severity="data.status === 'published' ? 'success' : data.status === 'draft' ? 'warning' : 'secondary'"
          />
        </template>
      </Column>
      <Column header="Actions" style="width: 25%">
        <template #body="{ data }">
          <div class="flex gap-2">
            <Button
              icon="pi pi-eye"
              size="small"
              severity="info"
              outlined
              @click="viewCourse(data.id)"
              v-tooltip.top="'View Course'"
            />
            <Button
              icon="pi pi-pencil"
              size="small"
              severity="secondary"
              outlined
              @click="editCourse(data.id)"
              v-tooltip.top="'Edit Course'"
            />
            <Button
              icon="pi pi-trash"
              size="small"
              severity="danger"
              outlined
              @click="deleteCourse(data.id)"
              v-tooltip.top="'Delete Course'"
            />
          </div>
        </template>
      </Column>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useCoursesStore } from '@/stores/courses';
import Divider from 'primevue/divider';
import Menubar from 'primevue/menubar';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import Button from 'primevue/button';
import Badge from 'primevue/badge';

const coursesStore = useCoursesStore();
const searchKey = ref("");

let searchTimeout: number;
watch(searchKey, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    coursesStore.searchCourses(newValue);
  }, 300);
});

const onPageChange = (event: any) => {
  const newPage = event.page + 1;
  const newLimit = event.rows;

  coursesStore.fetchCourses({
    page: newPage,
    limit: newLimit
  });
};

// Action handlers
const viewCourse = (courseId: string) => {
  console.log('View course:', courseId);
  // TODO: Navigate to course detail view
};

const editCourse = (courseId: string) => {
  console.log('Edit course:', courseId);
  // TODO: Open edit dialog or navigate to edit page
};

const deleteCourse = async (courseId: string) => {
  console.log('Delete course:', courseId);
  // TODO: Show confirmation dialog and delete course
  try {
    await coursesStore.deleteCourse(courseId);
    // Course will be automatically removed from the list by the store
  } catch (error) {
    console.error('Failed to delete course:', error);
    // TODO: Show error message to user
  }
};

onMounted(() => {
  coursesStore.fetchCourses();
});
</script>
