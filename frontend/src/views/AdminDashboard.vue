<template>
  <div class="min-h-screen flex dark-mode">
    <div class="fixed left-0 top-0 w-64 h-screen bg-surface-900 dark:bg-surface-950 flex flex-col z-40 hidden lg:flex">
      <SideMenu />
    </div>

    <div class="flex-1 flex flex-col lg:ml-64">
      <header class="lg:hidden bg-surface-900 dark:bg-surface-950 p-4 flex items-center justify-between">
        <h1 class="text-surface-0 font-semibold text-lg">Admin Dashboard</h1>
        <Button
          icon="pi pi-bars"
          severity="secondary"
          text
          @click="toggleMobileMenu"
          class="text-surface-0"
        />
      </header>

      <!-- Main Content -->
      <main class="flex-1 p-4 lg:p-6 bg-surface-50 dark:bg-surface-800 min-h-screen">
        <div class="max-w-7xl mx-auto">
          <router-view />
        </div>
      </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div
      v-if="showMobileMenu"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden"
      @click="closeMobileMenu"
    >
      <div class="fixed left-0 top-0 w-64 h-screen bg-surface-900 dark:bg-surface-950 flex flex-col">
        <SideMenu />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Button from 'primevue/button';
import SideMenu from '@/components/admin/SideMenu.vue';

// Mobile menu state
const showMobileMenu = ref(false);

// Mobile menu functions
const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value;
};

const closeMobileMenu = () => {
  showMobileMenu.value = false;
};
</script>
