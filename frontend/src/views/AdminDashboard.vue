<template>
  <div class="min-h-screen flex dark-mode">
    <div class="w-64 bg-surface-900 dark:bg-surface-950 flex flex-col">
      <!-- Top Section - User Avatar and Role -->
      <div class="p-6 border-b border-surface-700">
        <div class="text-center">
          <Avatar image="/images/admin-avatar.png" size="large" shape="circle" class="mb-3"
            style="background-color: var(--p-primary-color); color: white;" />
          <h3 class="text-surface-0 font-semibold text-lg mb-1">
            {{ authStore.user?.name || 'Admin User' }}
          </h3>
          <Badge :value="authStore.user?.role?.toUpperCase() || 'ADMIN'" severity="info" class="text-xs" />
        </div>
      </div>

      <!-- Middle Section - Menu Items -->
      <div class="flex-1 py-4">
        <nav class="space-y-2 px-4">
          <Button @click="navigateTo('/admin/dashboard')"
            :class="['w-full justify-start', isCurrentRoute('/admin/dashboard') ? 'bg-primary-500' : '']"
            severity="secondary" text>
            <i class="pi pi-home mr-3"></i>
            Dashboard
          </Button>

          <Button @click="navigateTo('/admin/users')"
            :class="['w-full justify-start', isCurrentRoute('/admin/users') ? 'bg-primary-500' : '']"
            severity="secondary" text disabled>
            <i class="pi pi-users mr-3"></i>
            Users
          </Button>

          <Button @click="navigateTo('/admin/courses')"
            :class="['w-full justify-start', isCurrentRoute('/admin/courses') ? 'bg-primary-500' : '']"
            severity="secondary" text disabled>
            <i class="pi pi-book mr-3"></i>
            Courses
          </Button>

          <Button @click="navigateTo('/admin/lessons')"
            :class="['w-full justify-start', isCurrentRoute('/admin/lessons') ? 'bg-primary-500' : '']"
            severity="secondary" text disabled>
            <i class="pi pi-file-edit mr-3"></i>
            Lessons
          </Button>

          <Button @click="navigateTo('/admin/quizzes')"
            :class="['w-full justify-start', isCurrentRoute('/admin/quizzes') ? 'bg-primary-500' : '']"
            severity="secondary" text disabled>
            <i class="pi pi-question-circle mr-3"></i>
            Quizzes
          </Button>

          <Button @click="navigateTo('/admin/settings')"
            :class="['w-full justify-start', isCurrentRoute('/admin/settings') ? 'bg-primary-500' : '']"
            severity="secondary" text disabled>
            <i class="pi pi-cog mr-3"></i>
            Settings
          </Button>
        </nav>
      </div>

      <!-- Bottom Section - Logout Button -->
      <div class="p-4 border-t border-surface-700">
        <Button @click="handleLogout" class="w-full" severity="danger" outlined>
          <i class="pi pi-sign-out mr-2"></i>
          Logout
        </Button>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col">
      <!-- Top Header -->
      <header class="bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700 p-4">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
            Admin Dashboard
          </h1>
          <div class="text-surface-600 dark:text-surface-400 text-sm">
            Welcome back, {{ authStore.user?.name || authStore.user?.email }}
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1 p-6 bg-surface-50 dark:bg-surface-800">
        <div class="max-w-7xl mx-auto">
          <Card>
            <template #content>
              <div class="text-center py-12">
                <i class="pi pi-home text-6xl text-primary mb-4"></i>
                <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-4">
                  Welcome to Admin Dashboard
                </h2>
                <p class="text-surface-600 dark:text-surface-400 text-lg mb-8">
                  Manage your Rwanda Road Rules Learning App from here.
                </p>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-users text-3xl text-primary mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Total Users</h3>
                        <p class="text-2xl font-bold text-primary">0</p>
                      </div>
                    </template>
                  </Card>

                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-book text-3xl text-green-500 mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Courses</h3>
                        <p class="text-2xl font-bold text-green-500">0</p>
                      </div>
                    </template>
                  </Card>

                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-file-edit text-3xl text-orange-500 mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Lessons</h3>
                        <p class="text-2xl font-bold text-orange-500">0</p>
                      </div>
                    </template>
                  </Card>

                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-question-circle text-3xl text-purple-500 mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Quizzes</h3>
                        <p class="text-2xl font-bold text-purple-500">0</p>
                      </div>
                    </template>
                  </Card>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge';
import Button from 'primevue/button';
import Card from 'primevue/card';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// Helper function to get user initials
const getInitials = (name: string): string => {
  if (!name) return 'U';
  const names = name.split(' ');
  if (names.length >= 2) {
    return (names[0][0] + names[1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

// Navigation helper
const navigateTo = (path: string) => {
  router.push(path);
};

// Check if current route matches
const isCurrentRoute = (path: string): boolean => {
  return route.path === path;
};

const handleLogout = async () => {
  await authStore.logout();
  router.push('/admin/login');
};
</script>
