<!-- <template>
  <div class="min-h-screen dark-mode">
    <!-- Header -->
    <Menubar :model="menuItems" class="border-b">
      <template #start>
        <h1 class="text-xl font-bold text-surface-900 dark:text-surface-0 ml-4">
          Admin Dashboard
        </h1>
      </template>
      
      <template #end>
        <div class="flex items-center gap-4 mr-4">
          <span class="text-surface-600 dark:text-surface-400">
            Welcome, {{ authStore.user?.name || authStore.user?.email }}
          </span>
          <Button
            @click="handleLogout"
            label="Logout"
            icon="pi pi-sign-out"
            severity="secondary"
            size="small"
          />
        </div>
      </template>
    </Menubar>

    <!-- Main Content -->
    <main class="p-6">
      <div class="max-w-7xl mx-auto">
        <Card>
          <template #content>
            <div class="text-center py-12">
              <i class="pi pi-home text-6xl text-primary mb-4"></i>
              <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-4">
                Admin Dashboard
              </h2>
              <p class="text-surface-600 dark:text-surface-400 text-lg mb-8">
                Welcome to the Rwanda Road Rules Learning App admin panel.
              </p>
              
              <!-- User Info Card -->
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <Card>
                  <template #content>
                    <div class="text-center">
                      <i class="pi pi-user text-3xl text-primary mb-3"></i>
                      <h3 class="text-lg font-semibold mb-2">User Profile</h3>
                      <p class="text-surface-600 dark:text-surface-400 text-sm">
                        <strong>Email:</strong> {{ authStore.user?.email }}<br>
                        <strong>Role:</strong> {{ authStore.user?.role }}<br>
                        <strong>Level:</strong> {{ authStore.user?.level || 'N/A' }}<br>
                        <strong>XP:</strong> {{ authStore.user?.xp || 0 }}
                      </p>
                    </div>
                  </template>
                </Card>
                
                <Card>
                  <template #content>
                    <div class="text-center">
                      <i class="pi pi-users text-3xl text-primary mb-3"></i>
                      <h3 class="text-lg font-semibold mb-2">Manage Users</h3>
                      <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
                        Create and manage user accounts
                      </p>
                      <Button
                        label="Users"
                        icon="pi pi-users"
                        size="small"
                        disabled
                      />
                    </div>
                  </template>
                </Card>
                
                <Card>
                  <template #content>
                    <div class="text-center">
                      <i class="pi pi-book text-3xl text-primary mb-3"></i>
                      <h3 class="text-lg font-semibold mb-2">Manage Courses</h3>
                      <p class="text-surface-600 dark:text-surface-400 text-sm mb-4">
                        Create and manage learning content
                      </p>
                      <Button
                        label="Courses"
                        icon="pi pi-book"
                        severity="secondary"
                        size="small"
                        disabled
                      />
                    </div>
                  </template>
                </Card>
              </div>
            </div>
          </template>
        </Card>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import Menubar from 'primevue/menubar';
import Card from 'primevue/card';
import Button from 'primevue/button';

const router = useRouter();
const authStore = useAuthStore();

const menuItems = ref([
  {
    label: 'Dashboard',
    icon: 'pi pi-home',
    command: () => router.push('/admin/dashboard')
  },
  {
    label: 'Users',
    icon: 'pi pi-users',
    disabled: true
  },
  {
    label: 'Courses',
    icon: 'pi pi-book',
    disabled: true
  }
]);

const handleLogout = async () => {
  await authStore.logout();
  router.push('/admin/login');
};
</script> -->
