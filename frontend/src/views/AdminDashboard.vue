<template>
  <div class="min-h-screen flex dark-mode">
    <div class="w-64 bg-surface-900 dark:bg-surface-950 flex flex-col">
       <SideMenu />
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col">
      <!-- Top Header -->
      <header class="bg-surface-0 dark:bg-surface-900 border-b border-surface-200 dark:border-surface-700 p-4">
        <div class="flex items-center justify-between">
          <h1 class="text-2xl font-bold text-surface-900 dark:text-surface-0">
            Admin Dashboard
          </h1>
          <div class="text-surface-600 dark:text-surface-400 text-sm">
            <!-- Welcome back, {{ authStore.user?.name || authStore.user?.email }} -->
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="flex-1 p-6 bg-surface-50 dark:bg-surface-800">
        <div class="max-w-7xl mx-auto">
          <Card>
            <template #content>
              <div class="text-center py-12">
                <i class="pi pi-home text-6xl text-primary mb-4"></i>
                <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-4">
                  Welcome to Admin Dashboard
                </h2>
                <p class="text-surface-600 dark:text-surface-400 text-lg mb-8">
                  Manage your Rwanda Road Rules Learning App from here.
                </p>

                <!-- Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-users text-3xl text-primary mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Total Users</h3>
                        <p class="text-2xl font-bold text-primary">0</p>
                      </div>
                    </template>
                  </Card>

                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-book text-3xl text-green-500 mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Courses</h3>
                        <p class="text-2xl font-bold text-green-500">0</p>
                      </div>
                    </template>
                  </Card>

                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-file-edit text-3xl text-orange-500 mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Lessons</h3>
                        <p class="text-2xl font-bold text-orange-500">0</p>
                      </div>
                    </template>
                  </Card>

                  <Card>
                    <template #content>
                      <div class="text-center">
                        <i class="pi pi-question-circle text-3xl text-purple-500 mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">Quizzes</h3>
                        <p class="text-2xl font-bold text-purple-500">0</p>
                      </div>
                    </template>
                  </Card>
                </div>
              </div>
            </template>
          </Card>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import Card from 'primevue/card';
import SideMenu from '@/components/admin/SideMenu.vue'
</script>
