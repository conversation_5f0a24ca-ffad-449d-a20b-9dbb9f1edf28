<template>
  <div class="min-h-screen dark-mode">
    <!-- Header -->
    <Menubar :model="menuItems" class="border-b">
      <template #start>
        <h1 class="text-xl font-bold text-surface-900 dark:text-surface-0 ml-4">
          Admin Dashboard
        </h1>
      </template>

      <template #end>
        <div class="flex items-center gap-4 mr-4">
          <span class="text-surface-600 dark:text-surface-400">
            Welcome, {{ authStore.user?.name }}
          </span>
          <Button
            @click="handleLogout"
            label="Logout"
            icon="pi pi-sign-out"
            severity="secondary"
            size="small"
          />
        </div>
      </template>
    </Menubar>

    <!-- Main Content -->
    <main class="p-6">
      <div class="max-w-7xl mx-auto">
        <Card>
          <template #content>
            <div class="text-center py-12">
              <i class="pi pi-home text-6xl text-primary mb-4"></i>
              <h2 class="text-2xl font-semibold text-surface-900 dark:text-surface-0 mb-4">
                Admin Dashboard
              </h2>
              <p class="text-surface-600 dark:text-surface-400 text-lg">
                Welcome to the Rwanda Road Rules Learning App admin panel.
              </p>
              <div class="mt-8">
                <Button
                  label="Manage Users"
                  icon="pi pi-users"
                  class="mr-4"
                  disabled
                />
                <Button
                  label="Manage Courses"
                  icon="pi pi-book"
                  severity="secondary"
                  disabled
                />
              </div>
            </div>
          </template>
        </Card>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import Menubar from 'primevue/menubar';
import Card from 'primevue/card';
import Button from 'primevue/button';

const router = useRouter();
const authStore = useAuthStore();

const menuItems = ref([
  {
    label: 'Dashboard',
    icon: 'pi pi-home',
    command: () => router.push('/admin/dashboard')
  },
  {
    label: 'Users',
    icon: 'pi pi-users',
    disabled: true
  },
  {
    label: 'Courses',
    icon: 'pi pi-book',
    disabled: true
  }
]);

const handleLogout = async () => {
  await authStore.logout();
  router.push('/admin/login');
};
</script>
