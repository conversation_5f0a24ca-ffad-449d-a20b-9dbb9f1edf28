<template>
  <div class="h-screen w-full flex dark-mode" style="background: var(--p-surface-ground);">
    <!-- Left Half - Empty or can be used for branding -->
    <div class="w-1/2 flex items-center justify-center">
      <!-- You can add branding, logo, or illustration here -->
      <div class="text-center">
        <i class="pi pi-car text-8xl text-primary mb-4"></i>
        <h1 class="text-4xl font-bold text-surface-900 dark:text-surface-0 mb-2">
          Rwanda Road Rules
        </h1>
        <p class="text-surface-600 dark:text-surface-400 text-lg">
          Learning Platform
        </p>
      </div>
    </div>

    <!-- Right Half - Login Card -->
    <div class="w-1/2 flex items-center justify-center p-6">
      <div class="w-full max-w-md">
        <Card class="shadow-lg">
          <template #title>
            <div class="text-center">
              <i class="pi pi-shield text-3xl text-primary mb-2"></i>
              <h2 class="text-2xl font-bold">Admin Login</h2>
            </div>
          </template>
          <template #content>
            <AdminLoginForm />
          </template>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Card } from 'primevue';
import AdminLoginForm from '@/components/AdminLoginForm.vue';
</script>