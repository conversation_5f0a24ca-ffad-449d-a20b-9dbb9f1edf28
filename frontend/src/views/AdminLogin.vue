<template>
  <div class="min-h-screen w-full flex items-center justify-center p-6 dark-mode"
    style="background: var(--p-surface-ground);">
    <Splitter>
      <SplitterPanel></SplitterPanel>
      <SplitterPanel>
        <Card>
          <template #title>
            Admin login
          </template>
          <template #content>
            <AdminLoginForm />
          </template>
        </Card>
      </SplitterPanel>
    </Splitter>
    <!-- <div class="w-full max-w-md">
      <Card class="shadow-lg">
        <template #header>
          <div class="text-center py-8 px-6">
            <div class="mb-4">
              <i class="pi pi-shield text-4xl text-primary"></i>
            </div>
            <h2 class="text-3xl font-bold text-surface-900 dark:text-surface-0 mb-2">
              Admin Login
            </h2>
            <p class="text-surface-600 dark:text-surface-400">
              Sign in to access the admin dashboard
            </p>
          </div>
        </template>

        <template #content>
          <div class="px-6 pb-6">
            <AdminLoginForm />
          </div>
        </template>
      </Card>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { SplitterPanel, Splitter, Card } from 'primevue';
import AdminLoginForm from '@/components/AdminLoginForm.vue';
</script>