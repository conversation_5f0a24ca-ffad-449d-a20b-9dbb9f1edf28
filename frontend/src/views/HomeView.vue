<script setup lang="ts">
import { useRouter } from 'vue-router';
import Card from 'primevue/card';
import Button from 'primevue/button';

const router = useRouter();

const goToAdminLogin = () => {
  router.push('/admin/login');
};
</script>

<template>
  <main class="min-h-screen p-6">
    <div class="max-w-4xl mx-auto">
      <Card>
        <template #header>
          <div class="text-center py-8">
            <i class="pi pi-car text-6xl text-primary mb-4"></i>
            <h1 class="text-4xl font-bold text-surface-900 dark:text-surface-0">
              Rwanda Road Rules Learning App
            </h1>
          </div>
        </template>

        <template #content>
          <div class="text-center">
            <p class="text-lg text-surface-600 dark:text-surface-400 mb-8">
              A gamified learning platform for teaching Rwandan traffic rules and regulations,
              inspired by applications like Duolingo.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card>
                <template #content>
                  <div class="text-center">
                    <i class="pi pi-book text-3xl text-primary mb-3"></i>
                    <h3 class="text-xl font-semibold mb-2">Modular Courses</h3>
                    <p class="text-surface-600 dark:text-surface-400">
                      Structured lessons covering all aspects of Rwandan traffic rules
                    </p>
                  </div>
                </template>
              </Card>

              <Card>
                <template #content>
                  <div class="text-center">
                    <i class="pi pi-trophy text-3xl text-primary mb-3"></i>
                    <h3 class="text-xl font-semibold mb-2">Gamification</h3>
                    <p class="text-surface-600 dark:text-surface-400">
                      Earn XP, maintain streaks, and unlock badges as you learn
                    </p>
                  </div>
                </template>
              </Card>

              <Card>
                <template #content>
                  <div class="text-center">
                    <i class="pi pi-question-circle text-3xl text-primary mb-3"></i>
                    <h3 class="text-xl font-semibold mb-2">Interactive Quizzes</h3>
                    <p class="text-surface-600 dark:text-surface-400">
                      Test your knowledge with multimedia-rich quiz questions
                    </p>
                  </div>
                </template>
              </Card>
            </div>

            <div class="flex justify-center gap-4">
              <Button
                label="Start Learning"
                icon="pi pi-play"
                size="large"
                disabled
              />
              <Button
                @click="goToAdminLogin"
                label="Admin Login"
                icon="pi pi-cog"
                severity="secondary"
                size="large"
              />
            </div>
          </div>
        </template>
      </Card>
    </div>
  </main>
</template>
