import api, { handleApiResponse, handleApiError } from '@/shared/lib/api';
import type { ApiResponse } from "@/shared/lib/api"
import type { AxiosResponse } from 'axios';

// Course types
export interface Course {
  id: string;
  name: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in hours
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  lessonsCount?: number;
  enrolledCount?: number;
}

export interface CreateCourseRequest {
  name: string;
  description: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  duration: number;
}

export interface UpdateCourseRequest extends Partial<CreateCourseRequest> {
  status?: 'draft' | 'published' | 'archived';
}

export interface CourseFilters {
  search?: string;
  level?: string;
  status?: string;
  page?: number;
  limit?: number;
}

export interface CoursesResponse {
  courses: Course[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Course Service
export class CourseService {
  /**
   * Get all courses with optional filters
   */
  static async getCourses(filters: CourseFilters = {}): Promise<CoursesResponse> {
    try {
      const params = new URLSearchParams();

      if (filters.search) params.append('search', filters.search);
      if (filters.level) params.append('level', filters.level);
      if (filters.status) params.append('status', filters.status);
      if (filters.page) params.append('page', filters.page.toString());
      if (filters.limit) params.append('limit', filters.limit.toString());

      const response: AxiosResponse<ApiResponse<CoursesResponse>> = await api.get(
        `/courses?${params.toString()}`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get course by ID
   */
  static async getCourseById(id: string): Promise<Course> {
    try {
      const response: AxiosResponse<ApiResponse<Course>> = await api.get(`/courses/${id}`);
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Create new course
   */
  static async createCourse(courseData: CreateCourseRequest): Promise<Course> {
    try {
      const response: AxiosResponse<ApiResponse<Course>> = await api.post(
        '/courses',
        courseData
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Update course
   */
  static async updateCourse(id: string, courseData: UpdateCourseRequest): Promise<Course> {
    try {
      const response: AxiosResponse<ApiResponse<Course>> = await api.put(
        `/courses/${id}`,
        courseData
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Delete course
   */
  static async deleteCourse(id: string): Promise<void> {
    try {
      await api.delete(`/courses/${id}`);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Publish course
   */
  static async publishCourse(id: string): Promise<Course> {
    try {
      const response: AxiosResponse<ApiResponse<Course>> = await api.patch(
        `/courses/${id}/publish`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Archive course
   */
  static async archiveCourse(id: string): Promise<Course> {
    try {
      const response: AxiosResponse<ApiResponse<Course>> = await api.patch(
        `/courses/${id}/archive`
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get course statistics
   */
  static async getCourseStats(): Promise<{
    totalCourses: number;
    publishedCourses: number;
    draftCourses: number;
    totalEnrollments: number;
  }> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await api.get('/courses/stats');
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }
}
