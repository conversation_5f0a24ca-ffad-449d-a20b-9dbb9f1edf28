<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 dark-mode">
    <Card class="w-full max-w-md">
      <template #header>
        <div class="text-center py-6">
          <h2 class="text-3xl font-bold text-surface-900 dark:text-surface-0">
            Admin Login
          </h2>
          <p class="mt-2 text-sm text-surface-600 dark:text-surface-400">
            Sign in to access the admin dashboard
          </p>
        </div>
      </template>

      <template #content>
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <div>
              <label for="email" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                Email address
              </label>
              <InputText id="email" v-model="form.email" type="email" placeholder="Enter your email" class="w-full"
                :invalid="!!authStore.error" required />
            </div>

            <div>
              <label for="password" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
                Password
              </label>
              <Password id="password" v-model="form.password" placeholder="Enter your password" class="w-full"
                :invalid="!!authStore.error" :feedback="false" toggle-mask required />
            </div>
          </div>

          <Message v-if="authStore.error" severity="error" :closable="false">
            {{ authStore.error }}
          </Message>

          <Button type="submit" :loading="authStore.isLoading" label="Sign in" class="w-full"
            :disabled="authStore.isLoading" />
        </form>
      </template>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import Card from 'primevue/card';
import InputText from 'primevue/inputtext';
import Password from 'primevue/password';
import Button from 'primevue/button';
import Message from 'primevue/message';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const form = reactive({
  email: '',
  password: ''
});

const handleSubmit = async () => {
  try {
    await authStore.login(form);

    // Redirect to intended page or dashboard
    const redirectTo = route.query.redirect as string || '/admin/dashboard';
    router.push(redirectTo);
  } catch (error) {
    // Error is handled by the store
  }
};

onMounted(() => {
  // Clear any previous errors
  authStore.clearError();

  // If already authenticated, redirect to dashboard
  if (authStore.isAuthenticated) {
    router.push('/admin/dashboard');
  }
});
</script>
