import { api } from '@/shared/lib/api';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  data: {
    token: string;
    expiresIn: string;
  },
  status: string;
  statusCode: number;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'teacher';
  level: number;
  xp: number;
}

export const authApi = {
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    const response = await api.post<LoginResponse>('/users/login', data);
    return response.data;
  },
  
  getProfile: async (): Promise<UserProfile> => {
    const response = await api.get<UserProfile>('/users/profile');
    return response.data;
  },
  
  logout: async (): Promise<void> => {
    localStorage.removeItem('token');
  }
};
