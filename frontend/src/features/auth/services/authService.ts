import api, { handleApiResponse, handleApiError } from '@/shared/lib/api';
import type { ApiResponse } from '@/shared/lib/api'
import type { AxiosResponse } from 'axios';

// Auth types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  expiresIn: string;
}

export interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'teacher' | 'student';
  level: number;
  xp: number;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  role?: 'teacher' | 'student';
}

// Auth Service
export class AuthService {
  /**
   * Login user with email and password
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response: AxiosResponse<ApiResponse<LoginResponse>> = await api.post(
        '/users/login',
        credentials
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<UserProfile> {
    try {
      const response: AxiosResponse<ApiResponse<UserProfile>> = await api.get('/users/profile');
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Register new user (admin only)
   */
  static async register(userData: RegisterRequest): Promise<UserProfile> {
    try {
      const response: AxiosResponse<ApiResponse<UserProfile>> = await api.post(
        '/users/register',
        userData
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const response: AxiosResponse<ApiResponse<UserProfile>> = await api.put(
        '/users/profile',
        profileData
      );
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Change user password
   */
  static async changePassword(passwordData: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    try {
      await api.put('/users/change-password', passwordData);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }

  /**
   * Logout user (clear local storage)
   */
  static async logout(): Promise<void> {
    localStorage.removeItem('token');
    // Optional: Call backend logout endpoint if you have one
    // await api.post('/users/logout');
  }

  /**
   * Refresh token (if you implement refresh tokens)
   */
  static async refreshToken(): Promise<LoginResponse> {
    try {
      const response: AxiosResponse<ApiResponse<LoginResponse>> = await api.post('/users/refresh');
      return handleApiResponse(response);
    } catch (error: any) {
      throw new Error(handleApiError(error));
    }
  }
}
