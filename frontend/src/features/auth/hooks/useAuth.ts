'use client';

import { useState, useEffect } from 'react';
import { authApi, UserProfile } from '../api/authApi';

interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  error: string | null;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    error: null,
  });

  // Check if user is logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('token');

      if (!token) {
        setAuthState({
          user: null,
          isLoading: false,
          error: null,
        });
        return;
      }

      try {
        const user = await authApi.getProfile();
        setAuthState({
          user,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        localStorage.removeItem('token');
        setAuthState({
          user: null,
          isLoading: false,
          error: 'Session expired. Please log in again.',
        });
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await authApi.login({ email, password });
      localStorage.setItem('token', response.data.token);

      // Fetch user profile after successful login
      const user = await authApi.getProfile();

      setAuthState({
        user,
        isLoading: false,
        error: null,
      });

      return true;
    } catch (error: any) {
      setAuthState({
        user: null,
        isLoading: false,
        error: error.response?.data?.message || 'Login failed. Please try again.',
      });

      return false;
    }
  };

  // Logout function
  const logout = async () => {
    await authApi.logout();
    setAuthState({
      user: null,
      isLoading: false,
      error: null,
    });
  };

  return {
    user: authState.user,
    isLoading: authState.isLoading,
    error: authState.error,
    isAuthenticated: !!authState.user,
    isAdmin: authState.user?.role === 'admin',
    isTeacher: authState.user?.role === 'teacher',
    login,
    logout,
  };
}
