import { Inter } from 'next/font/google';
import type { Metadata } from 'next';
import '../globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  title: 'Admin Portal | Uruhushya',
  description: 'Administrative dashboard for the Uruhushya Rwanda Road Rules Learning Platform',
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark h-full">
      <body className={`${inter.variable} font-sans antialiased min-h-screen bg-[#161616]`}>
        <div className="min-h-screen bg-admin-background text-admin-textPrimary">
          {children}
        </div>
      </body>
    </html>
  );
}
