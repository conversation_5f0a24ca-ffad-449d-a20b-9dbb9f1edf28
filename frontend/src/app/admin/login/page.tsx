'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/features/auth/hooks/useAuth';

export default function AdminLoginPage() {
  const router = useRouter();
  const { login, error: authError } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const success = await login(formData.email, formData.password);

      if (success) {
        router.push('/admin/dashboard');
      } else {
        setError(authError || 'Login failed. Please try again.');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#262626] p-4">
      <div className="w-full max-w-sm">
        <div className="bg-[#D7D7D7] rounded-lg shadow-xl overflow-hidden">
          <div className="flex flex-col items-center mt-3 mt-2">
            <h2 className="text-lg font-bold text-[#262626]">Login</h2>
            {!error && (
              <p>Enter your admin credentials</p>
            )}
            {error && (
              <p className="text-[#ff4d4f]">
                {error}
              </p>
            )}
          </div>

          <div className="p-2">
            <form onSubmit={handleSubmit} className="space-y-2" autoComplete="off">
              <div className="space-y-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={handleChange}
                  placeholder="<EMAIL>"
                  autoComplete="email"
                  className="flex h-10 w-full rounded-md bg-[#EBEBEB] focus:bg-[#FEFEFE] px-3 py-2 pl-3 text-sm text-[#262626] placeholder:text-[#888888] focus:outline-none transition-colors"
                />
              </div>
              <div className="space-y-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="••••••••"
                  autoComplete="current-password"
                  className="flex h-10 w-full rounded-md bg-[#EBEBEB] focus:bg-[#FEFEFE] px-3 py-2 pl-3 text-sm text-[#262626] placeholder:text-[#888888] focus:outline-none transition-colors"
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="relative inline-flex w-full items-center justify-center rounded-md text-sm font-medium h-10 py-2 px-4 bg-[#262626] text-[#f6f6f6] cursor-pointer"
              >
                {isLoading && (
                  <svg
                    className="mr-2 h-4 w-4 animate-spin"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                )}
                Sign In
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
