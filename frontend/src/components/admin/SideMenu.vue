<template>
    <div class="h-full flex flex-col">
        <!-- User Profile Section -->
        <div class="p-6 border-b border-surface-700 flex-shrink-0">
            <div class="text-center">
                <Avatar image="/images/avatar/admin.png" size="xlarge" shape="circle" class="mb-3" />
                <h3 class="text-surface-0 font-semibold text-lg mb-1">
                    {{ authStore.user?.name || 'Admin User' }}
                </h3>
                <Badge :value="authStore.user?.role?.toUpperCase() || 'ADMIN'" severity="success" class="text-xs" />
            </div>
        </div>

        <!-- Navigation Section -->
        <div class="flex-1 py-4 overflow-y-auto">
            <nav class="space-y-2 px-4">
            <Button @click="navigateTo('/admin/dashboard')"
                :class="['w-full justify-start', isCurrentRoute('/admin/dashboard') ? 'bg-primary-500' : '']"
                severity="secondary" text>
                <i class="pi pi-home mr-3"></i>
                Dashboard
            </Button>

            <Button @click="navigateTo('/admin/users')"
                :class="['w-full justify-start', isCurrentRoute('/admin/users') ? 'bg-primary-500' : '']"
                severity="secondary" text disabled>
                <i class="pi pi-users mr-3"></i>
                Users
            </Button>

            <Button @click="navigateTo('/admin/dashboard/courses')"
                :class="['w-full justify-start', isCurrentRoute('/admin/dashboard/courses') ? 'bg-primary-500' : '']"
                severity="secondary" text>
                <i class="pi pi-book mr-3"></i>
                Courses
            </Button>

            <Button @click="navigateTo('/admin/lessons')"
                :class="['w-full justify-start', isCurrentRoute('/admin/lessons') ? 'bg-primary-500' : '']"
                severity="secondary" text disabled>
                <i class="pi pi-file-edit mr-3"></i>
                Lessons
            </Button>

            <Button @click="navigateTo('/admin/quizzes')"
                :class="['w-full justify-start', isCurrentRoute('/admin/quizzes') ? 'bg-primary-500' : '']"
                severity="secondary" text disabled>
                <i class="pi pi-question-circle mr-3"></i>
                Quizzes
            </Button>

            <Button @click="navigateTo('/admin/settings')"
                :class="['w-full justify-start', isCurrentRoute('/admin/settings') ? 'bg-primary-500' : '']"
                severity="secondary" text disabled>
                <i class="pi pi-cog mr-3"></i>
                Settings
            </Button>
            </nav>
        </div>

        <!-- Bottom Section - Logout Button -->
        <div class="p-4 border-t border-surface-700 flex-shrink-0">
            <Button @click="handleLogout" class="w-full" severity="danger" outlined>
                <i class="pi pi-sign-out mr-2"></i>
                Logout
            </Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import Avatar from 'primevue/avatar';
import Badge from 'primevue/badge';
import Button from 'primevue/button';

import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

// Navigation helper
const navigateTo = (path: string) => {
    router.push(path);
};

// Check if current route matches
const isCurrentRoute = (path: string): boolean => {
    return route.path === path;
};

const handleLogout = async () => {
    await authStore.logout();
    router.push('/admin/login');
};
</script>