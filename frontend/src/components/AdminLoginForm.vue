<template>
  <form class="space-y-6" @submit.prevent="handleSubmit">
    <div class="space-y-4">
      <div class="mb-4" v-if="authStore.error">
        <Message v-if="authStore.error" severity="error" :closable="false">
          {{ authStore.error }}
        </Message>
      </div>

      <div>
        <label for="email" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
          Email address
        </label>
        <InputText id="email" v-model="form.email" type="email" placeholder="Enter your email" class="w-full"
          :invalid="!!authStore.error" required />
      </div>

      <Divider />

      <div>
        <label for="password" class="block text-sm font-medium text-surface-900 dark:text-surface-0 mb-2">
          Password
        </label>
        <Password id="password" v-model="form.password" placeholder="Enter your password" class="w-full"
          :invalid="!!authStore.error" :feedback="false" toggle-mask required fluid />
      </div>
    </div>

    <Divider />

    <Button type="submit" :loading="authStore.isLoading" label="Sign in" class="w-full"
      :disabled="authStore.isLoading" />
  </form>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '@/stores/auth';
import InputText from 'primevue/inputtext';
import Password from 'primevue/password';
import Button from 'primevue/button';
import Message from 'primevue/message';
import Divider from 'primevue/divider';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();

const form = reactive({
  email: '',
  password: ''
});

const handleSubmit = async () => {
  try {
    await authStore.login(form);

    const redirectTo = route.query.redirect as string || '/admin/dashboard';
    router.push(redirectTo);
  } catch (error) {
  }
};

onMounted(() => {
  authStore.clearError();

  if (authStore.isAuthenticated) {
    router.push('/admin/dashboard');
  }
});
</script>
