/* Custom styles to enhance PrimeVue with Rwanda Road Rules theme */

/* Dark mode specific overrides */
.dark-mode {
  background-color: #161616;
  color: #f6f6f6;
}

/* Custom CSS variables for consistent theming */
:root {
  /* Admin theme colors */
  --admin-primary: #0076ff;
  --admin-accent: #f2cb0f;
  --admin-background: #161616;
  --admin-surface: #313131;
  --admin-text-primary: #f6f6f6;
  --admin-text-secondary: #888888;
  --admin-border: #444444;
  --admin-error: #ff4d4f;
  --admin-success: #52c41a;
  --admin-warning: #faad14;
  
  /* User theme colors */
  --user-primary: #1e3a8a;
  --user-accent: #10b981;
  --user-background: #f9fafb;
  --user-surface: #ffffff;
  --user-text-primary: #111827;
  --user-text-secondary: #6b7280;
  --user-border: #e5e7eb;
  --user-error: #ef4444;
  --user-success: #10b981;
  --user-warning: #f59e0b;
}

/* Enhanced card styling for admin interface */
.dark-mode .p-card {
  background: var(--admin-surface);
  border: 1px solid var(--admin-border);
  color: var(--admin-text-primary);
}

.dark-mode .p-card .p-card-title {
  color: var(--admin-text-primary);
}

.dark-mode .p-card .p-card-content {
  color: var(--admin-text-primary);
}

/* Enhanced button styling */
.dark-mode .p-button {
  background: var(--admin-primary);
  border-color: var(--admin-primary);
}

.dark-mode .p-button:hover {
  background: #1e40af;
  border-color: #1e40af;
}

.dark-mode .p-button.p-button-secondary {
  background: var(--admin-surface);
  border-color: var(--admin-border);
  color: var(--admin-text-primary);
}

.dark-mode .p-button.p-button-secondary:hover {
  background: #404040;
  border-color: #525252;
}

/* Enhanced input styling */
.dark-mode .p-inputtext {
  background: var(--admin-surface);
  border-color: var(--admin-border);
  color: var(--admin-text-primary);
}

.dark-mode .p-inputtext:focus {
  border-color: var(--admin-primary);
  box-shadow: 0 0 0 0.2rem rgba(0, 118, 255, 0.2);
}

.dark-mode .p-password .p-inputtext {
  background: var(--admin-surface);
  border-color: var(--admin-border);
  color: var(--admin-text-primary);
}

/* Enhanced menubar styling */
.dark-mode .p-menubar {
  background: var(--admin-surface);
  border-color: var(--admin-border);
}

.dark-mode .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content {
  color: var(--admin-text-primary);
}

.dark-mode .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content:hover {
  background: #404040;
}

/* Enhanced message styling */
.dark-mode .p-message.p-message-error {
  background: rgba(255, 77, 79, 0.1);
  border-color: var(--admin-error);
  color: var(--admin-error);
}

/* Custom utility classes */
.admin-bg {
  background-color: var(--admin-background);
}

.admin-surface {
  background-color: var(--admin-surface);
}

.admin-text {
  color: var(--admin-text-primary);
}

.admin-text-secondary {
  color: var(--admin-text-secondary);
}

.user-bg {
  background-color: var(--user-background);
}

.user-surface {
  background-color: var(--user-surface);
}

.user-text {
  color: var(--user-text-primary);
}

.user-text-secondary {
  color: var(--user-text-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .split-screen {
    flex-direction: column;
  }
  
  .split-screen > div {
    width: 100% !important;
  }
}
