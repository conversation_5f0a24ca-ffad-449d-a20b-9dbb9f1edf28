import { definePreset } from '@primeuix/themes';
import Aura from '@primeuix/themes/aura';

// Custom color palette for Rwanda Road Rules Learning App
const customColors = {
  // Admin theme colors (dark)
  admin: {
    primary: '#0076ff',
    accent: '#f2cb0f',
    background: '#161616',
    surface: '#313131',
    textPrimary: '#f6f6f6',
    textSecondary: '#888888',
    border: '#444444',
    error: '#ff4d4f',
    success: '#52c41a',
    warning: '#faad14',
  },
  // User theme colors (light)
  user: {
    primary: '#1e3a8a',
    accent: '#10b981',
    background: '#f9fafb',
    surface: '#ffffff',
    textPrimary: '#111827',
    textSecondary: '#6b7280',
    border: '#e5e7eb',
    error: '#ef4444',
    success: '#10b981',
    warning: '#f59e0b',
  }
};

// Custom Aura preset with Rwanda Road Rules colors
export const CustomAura = definePreset(Aura, {
  semantic: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: customColors.admin.primary, // '#0076ff'
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
      950: '#172554'
    },
    colorScheme: {
      light: {
        primary: {
          color: customColors.user.primary,
          contrastColor: '#ffffff',
          hoverColor: '#1e40af',
          activeColor: '#1d4ed8'
        },
        surface: {
          0: '#ffffff',
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617'
        }
      },
      dark: {
        primary: {
          color: customColors.admin.primary,
          contrastColor: '#ffffff',
          hoverColor: '#3b82f6',
          activeColor: '#2563eb'
        },
        surface: {
          0: customColors.admin.background, // '#161616'
          50: '#1a1a1a',
          100: '#262626',
          200: customColors.admin.surface, // '#313131'
          300: '#404040',
          400: '#525252',
          500: '#737373',
          600: '#a3a3a3',
          700: '#d4d4d4',
          800: '#e5e5e5',
          900: customColors.admin.textPrimary, // '#f6f6f6'
          950: '#fafafa'
        }
      }
    }
  }
});

export { customColors };
