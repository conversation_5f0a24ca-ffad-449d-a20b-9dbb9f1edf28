{"name": "frontend-vue", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@primevue/themes": "^4.3.5", "@tailwindcss/forms": "^0.5.10", "@tanstack/vue-query": "^5.81.2", "@vueuse/core": "^13.4.0", "@vueuse/head": "^2.0.0", "@vueuse/motion": "^3.0.3", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "vue": "^3.5.13", "vue-router": "^4.5.0", "zod": "^3.25.67"}, "devDependencies": {"@testing-library/vue": "^8.1.0", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "postcss": "^8.5.6", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}