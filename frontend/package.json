{"name": "<PERSON><PERSON><PERSON><PERSON>-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.0.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5"}}