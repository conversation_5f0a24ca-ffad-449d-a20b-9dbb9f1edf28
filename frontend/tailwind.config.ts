import type { Config } from 'tailwindcss';
import { adminPalette, userPalette } from './src/shared/theme/colors';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/features/**/*.{js,ts,jsx,tsx,mdx}',
    './src/shared/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        admin: adminPalette,
        user: userPalette,
      },
      fontFamily: {
        sans: ['var(--font-inter)', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'admin': '0 4px 14px 0 rgba(0, 118, 255, 0.39)',
      },
      backgroundImage: {
        'admin-gradient': 'linear-gradient(to right, #0076ff, #00a3ff)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
};

export default config;
